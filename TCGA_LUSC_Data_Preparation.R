setwd("C:/Users/<USER>/Desktop/code/TCGA/肝癌")
dir.create("TCGA_LUSC_Data", recursive = TRUE)

#2.1 下载数据
library(TCGAbiolinks)
##(1) 获取下载目标文件
query <- GDCquery(project = "TCGA-LUSC",
                  data.category = "Transcriptome Profiling",
                  data.type = "Gene Expression Quantification")

##(2) 下载数据到本地
# files.per.chunk = 10 表示分批下载，每批下载10个病人的数据，可避免中途报错，而前功尽弃。
GDCdownload(
  query = query,
  files.per.chunk = 10,
  directory = "TCGA_LUSC_Data"
)



##(3) 加载数据至当前环境
data <- GDCprepare(query, save = T,, directory = "TCGA_LUSC_Data", save.filename = "TCGA_LUSC_RNAseq_hg38.rda")















#2.3 整理数据
##(1)更改表达矩阵行名/基因名格式，筛选基因
rownames(data) = rowData(data)$gene_name
data = data[!duplicated(rownames(data)),]
data = data[rowData(data)$gene_type=="protein_coding",]
dim(data)
# [1] 19934   562
assay(data, "unstranded")[1:3,1:3]
#TCGA-56-7221-01A-11R-2045-07 TCGA-43-7657-01A-31R-2125-07 TCGA-43-7657-11A-01R-2125-07
#TSPAN6                         6066                         2329                         2459
#TNMD                              9                            0                            0
#DPM1                           3344                         2533                         1734


##(2) 分析样本间相关性，鉴定离群点样本
CorOutliers <- TCGAanalyze_Preprocessing(data, cor.cut = 0, 
                                         filename="LUSC_RNAseq_hg38_cor.png")


##(3) 标准化
dataNorm <- TCGAanalyze_Normalization(tabDF = data, geneInfo =  geneInfo) #没有log处理
dim(dataNorm)
# [1] 16491   562
class(dataNorm) 
# [1] "matrix" "array"
dataNorm[1:3,1:3]
#TCGA-56-7221-01A-11R-2045-07 TCGA-43-7657-01A-31R-2125-07 TCGA-43-7657-11A-01R-2125-07
#TSPAN6                         6066                         2329                         2459
#TNMD                              9                            0                            0


exp_data = dataNorm


#2.4 差异分析

######edgeR

##(1) 确定分组样本
samplesNT <- TCGAquery_SampleTypes(barcode = colnames(exp_data),
                                   typesample = c("NT"))
samplesTP <- TCGAquery_SampleTypes(barcode = colnames(exp_data), 
                                   typesample = c("TP"))

##(2) 差异分析
dataDEGs <- TCGAanalyze_DEA(mat1 = exp_data[,samplesNT],
                            mat2 = exp_data[,samplesTP],
                            Cond1type = "Normal",
                            Cond2type = "Tumor",
                            pipeline = "edgeR",
                            method = "glmLRT")
##(3) 整理结果
DEGs <- TCGAanalyze_LevelTab(dataDEGs,"Tumor","Normal",
                             exp_data[,samplesTP],exp_data[,samplesNT])
DEGs = DEGs[order(DEGs$FDR),]
DEGs$Direct = ifelse(DEGs$logFC>0, "Up", "Down")
head(DEGs)
#mRNA     logFC           FDR      Delta     Tumor    Normal Direct
#ITLN1     ITLN1 -7.700238 1.245301e-295  -189.4168  24.59883  5605.608   Down
#CLEC3B   CLEC3B -4.696588 2.059882e-258  -460.1277  97.97065  3330.980   Down
#ROBO4     ROBO4 -3.507030 5.730587e-257 -2147.8054 612.42857  9511.059   Down
#RTKN2     RTKN2 -3.938754 6.730353e-256 -3044.2249 772.89041 15316.451   Down
#RAMP2     RAMP2 -3.052191 1.321582e-234 -1610.0038 527.49119  5934.353   Down
#HSD17B6 HSD17B6 -3.878059 8.879637e-226 -1143.3976 294.83757  5677.765   Down

DEGs = DEGs[order(DEGs$FDR),]
DEGs$Direct = ifelse(DEGs$logFC>0, "Up", "Down")
table(DEGs$FDR<0.01,DEGs$Direct)
#Down   Up
#FALSE 1695 2631
#TRUE  4024 8141
DEGs_sig = subset(DEGs, FDR<0.01)
table(DEGs_sig$Direct, abs(DEGs_sig$logFC)>1)
#FALSE TRUE
#Down  1822 2202
#Up    3792 4349






#获取病人的临床数据
#clinical <- GDCquery_clinic(project = "TCGA-LUSC", type = "clinical")
clinical <- GDCquery_clinic(project = "TCGA-LUSC", type = "clinical")
clinical[1:4,1:4]
#project submitter_id synchronous_malignancy ajcc_pathologic_stage
#1 TCGA-LUSC TCGA-22-1011                     No              Stage IB
#2 TCGA-LUSC TCGA-O2-A52W                     No               Stage I
#3 TCGA-LUSC TCGA-77-8150                     No            Stage IIIA
#4 TCGA-LUSC TCGA-22-5478                     No              Stage IB



