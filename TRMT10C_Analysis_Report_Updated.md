# TRMT10C基因在TCGA LUSC中的表达差异分析报告
## 方案1：每个亚组都包含正常样本作为对照 + 合并图表

## 分析概述

本分析专门针对TRMT10C基因在TCGA肺鳞癌(LUSC)数据集中的表达模式，采用**方案1**：每个亚组都包含正常样本作为对照，实现了正常vs肿瘤的差异分析，以及在不同临床亚组内的正常vs肿瘤对比分析。**新增了合并图表功能**，将不同分期以及正常样本的mRNA表达在同一图中展现。

## 数据概况

### 样本信息
- **总样本数**: 562个
- **正常样本**: 51个
- **肿瘤样本**: 511个
- **TRMT10C基因**: 成功检测到，表达范围226-8634

### 临床特征分布

#### 年龄分组 (肿瘤样本)
- **<60岁**: 110人 (21.5%)
- **60-70岁**: 203人 (39.7%) 
- **>70岁**: 189人 (37.0%)
- **缺失**: 9人 (1.8%)

#### 分期分布 (肿瘤样本)
- **Stage I**: 251人 (49.1%)
- **Stage II**: 164人 (32.1%)
- **Stage III**: 85人 (16.6%)
- **Stage IV**: 7人 (1.4%)
- **缺失**: 4人 (0.8%)

#### 性别分布 (肿瘤样本)
- **男性**: 379人 (74.2%)
- **女性**: 132人 (25.8%)

#### 人种分布 (肿瘤样本)
- **白人**: 358人 (70.1%)
- **未报告**: 112人 (21.9%)
- **黑人**: 31人 (6.1%)
- **亚洲人**: 9人 (1.8%)
- **未知**: 1人 (0.2%)

#### 生存状态 (肿瘤样本)
- **存活**: 287人 (56.2%)
- **死亡**: 224人 (43.8%)

### 正常样本在各亚组中的分布

#### 年龄分组中的正常样本
- **<60岁**: 10个正常样本
- **60-70岁**: 21个正常样本
- **>70岁**: 20个正常样本

#### 分期分组中的正常样本
- **Stage I**: 27个正常样本
- **Stage II**: 17个正常样本
- **Stage III**: 6个正常样本
- **Stage IV**: 1个正常样本 (样本数不足)

## 主要发现

### 1. 正常 vs 肿瘤表达差异

**关键结果**:
- **正常样本TRMT10C表达中位数**: 1174
- **肿瘤样本TRMT10C表达中位数**: 1860
- **Wilcoxon检验p值**: 8.88 × 10⁻⁹ (高度显著)
- **效应大小**: 1.584 (肿瘤/正常)

**结论**: TRMT10C在肿瘤组织中显著高表达，表达水平约为正常组织的1.6倍。

### 2. 方案1实现特点

- ✅ **每个亚组内都包含正常vs肿瘤对比**
- ✅ **自动处理样本数不足的情况** (如Stage IV)
- ✅ **生成合并图表**，在同一图中展示不同分期和正常样本
- ✅ **自动标注样本数量**，便于评估统计可靠性

## 生成的文件

### 数据文件
1. **TRMT10C_integrated_data.tsv** - 完整的整合数据，包含表达和临床信息
2. **TRMT10C_tumor_data.tsv** - 仅肿瘤样本数据
3. **TRMT10C_expression_matrix.tsv** - TRMT10C表达矩阵
4. **TRMT10C_analysis_results.rda** - R格式的分析结果

### 图表文件

#### 基础差异分析
- **TRMT10C_Normal_vs_Tumor.png** - 正常vs肿瘤表达比较

#### 🆕 分期合并图表（核心图表）

**主要合并图表:**
- **TRMT10C_Stage_Only_Combined.png** - 仅按分期分组的正常vs肿瘤对比
- **TRMT10C_Stage_Age_Combined.png** - 分期+年龄分组的正常vs肿瘤对比
- **TRMT10C_Stage_Gender_Combined.png** - 分期+性别分组的正常vs肿瘤对比  
- **TRMT10C_Stage_Survival_Combined.png** - 分期+生存状态分组的正常vs肿瘤对比

**特点:**
- 每张图包含所有分期的数据
- 在同一图中显示正常和肿瘤样本的表达差异
- 自动标注每个亚组的样本数量 (n=XX)
- Stage IV样本数不足时会特别标注
- 使用分组箱线图 + 散点图的组合展示

#### 综合亚组分析（方案1：正常vs肿瘤对比）
- **TRMT10C_Comprehensive_Age_Groups.png** - 年龄分组内正常vs肿瘤对比
- **TRMT10C_Comprehensive_Gender.png** - 性别分组内正常vs肿瘤对比
- **TRMT10C_Comprehensive_Survival.png** - 生存状态分组内正常vs肿瘤对比
- **TRMT10C_Comprehensive_Stage.png** - 分期分组内正常vs肿瘤对比

## 技术特点

### 分析方法
- **统计检验**: Wilcoxon秩和检验(两组比较)
- **可视化**: 分组箱线图 + 散点图，包含样本数标注
- **数据处理**: 自动处理缺失值，样本数不足时特别标注

### 质量控制
- 最小样本数要求: 每个亚组至少2个正常样本
- 数据完整性检查
- 自适应分析策略
- 样本数量自动标注

### 🆕 合并图表优势
1. **信息密度高**: 一张图展示多个分期的信息
2. **对比直观**: 正常vs肿瘤在同一视野内对比
3. **样本数透明**: 自动显示每个亚组的样本数
4. **适合发表**: 高分辨率，布局清晰

## 结论

1. **TRMT10C在肺鳞癌中显著高表达**，提示其可能在肿瘤发生发展中发挥重要作用
2. **方案1成功实现**，每个亚组都包含正常样本作为对照
3. **合并图表功能**大大提高了数据展示的效率和直观性
4. **生成的数据和图表**可直接用于科研论文和进一步的生物信息学分析

## 推荐使用的图表

### 用于论文发表的核心图表:
1. **TRMT10C_Normal_vs_Tumor.png** - 基础差异展示
2. **TRMT10C_Stage_Only_Combined.png** - 分期分析主图
3. **TRMT10C_Stage_Age_Combined.png** - 年龄亚组分析
4. **TRMT10C_Stage_Gender_Combined.png** - 性别亚组分析

### 用于补充材料:
- 其他综合分析图表
- TSV数据文件用于数据共享

## 使用说明

所有生成的TSV文件可以直接在Excel、R或Python中打开进行进一步分析。图表文件为高分辨率PNG格式，适合用于论文发表。合并图表特别适合在有限的版面中展示丰富的分析结果。

---

*分析完成时间: 2024年*  
*数据来源: TCGA-LUSC项目*  
*分析工具: R + TCGAbiolinks + ggplot2 + dplyr*  
*分析策略: 方案1 + 合并图表*
