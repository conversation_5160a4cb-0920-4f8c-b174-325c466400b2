# TRMT10C基因扩展亚组分析 - Nature风格
# 包含吸烟状态、TNM分期、既往恶性肿瘤史等新维度

setwd("C:/Users/<USER>/Desktop/code/TCGA/肺癌")

# 加载必要的包
library(TCGAbiolinks)
library(SummarizedExperiment)
library(ggplot2)
library(dplyr)
library(RColorBrewer)
library(cowplot)

# 检查并安装ggpubr包
if (!require(ggpubr, quietly = TRUE)) {
  install.packages("ggpubr")
  library(ggpubr)
}

# 1. 加载和预处理数据
print("=== 加载数据 ===")
load("TCGA_LUSC_RNAseq_hg38.rda")

# 处理表达数据
rownames(data) <- rowData(data)$gene_name
data <- data[!duplicated(rownames(data)), ]
data <- data[rowData(data)$gene_type == "protein_coding", ]

exp_matrix <- assay(data, "unstranded")
samplesNT <- TCGAquery_SampleTypes(barcode = colnames(exp_matrix), typesample = c("NT"))
samplesTP <- TCGAquery_SampleTypes(barcode = colnames(exp_matrix), typesample = c("TP"))

# 获取临床数据
clinical <- GDCquery_clinic(project = "TCGA-LUSC", type = "clinical")
clinical$sample_id <- substr(clinical$submitter_id, 1, 12)

# 创建整合数据
trmt10c_expr <- exp_matrix["TRMT10C", ]
sample_info <- data.frame(
  sample_barcode = names(trmt10c_expr),
  trmt10c_expression = as.numeric(trmt10c_expr),
  sample_type = ifelse(names(trmt10c_expr) %in% samplesNT, "Normal", "Tumor"),
  stringsAsFactors = FALSE
)
sample_info$sample_id <- substr(sample_info$sample_barcode, 1, 12)
merged_data <- merge(sample_info, clinical, by = "sample_id", all.x = TRUE)

# 只保留肿瘤样本进行亚组分析
tumor_data <- merged_data[merged_data$sample_type == "Tumor", ]

# Nature风格主题
nature_theme <- function() {
  theme_classic() +
    theme(
      text = element_text(color = "black"),
      plot.title = element_text(size = 14, face = "bold", hjust = 0.5,
                               margin = margin(b = 20)),
      axis.title = element_text(size = 12, face = "bold", color = "black"),
      axis.text = element_text(size = 10, color = "black"),
      axis.line = element_line(color = "black", linewidth = 0.5),
      axis.ticks = element_line(color = "black", linewidth = 0.5),
      legend.title = element_text(size = 11, face = "bold"),
      legend.text = element_text(size = 10),
      legend.position = "bottom",
      panel.background = element_rect(fill = "white", color = NA),
      panel.grid = element_blank(),
      plot.margin = margin(20, 20, 20, 20),
      strip.text = element_text(size = 11, face = "bold", color = "black"),
      strip.background = element_rect(fill = "grey95", color = "black", linewidth = 0.5)
    )
}

# 2. 吸烟状态分析
print("=== 吸烟状态分析 ===")

# 处理吸烟状态数据
tumor_data$smoking_status_simple <- tumor_data$tobacco_smoking_status
tumor_data$smoking_status_simple[tumor_data$smoking_status_simple == "Current Reformed Smoker for < or = 15 yrs"] <- "Reformed ≤15y"
tumor_data$smoking_status_simple[tumor_data$smoking_status_simple == "Current Reformed Smoker for > 15 yrs"] <- "Reformed >15y"
tumor_data$smoking_status_simple[tumor_data$smoking_status_simple == "Current Reformed Smoker, Duration Not Specified"] <- "Reformed (Unknown)"
tumor_data$smoking_status_simple[tumor_data$smoking_status_simple == "Current Smoker"] <- "Current"
tumor_data$smoking_status_simple[tumor_data$smoking_status_simple == "Lifelong Non-Smoker"] <- "Never"

# 统计各组样本数
smoking_counts <- table(tumor_data$smoking_status_simple, useNA = "always")
print("吸烟状态分布:")
print(smoking_counts)

# 创建吸烟状态图
smoking_data <- tumor_data[!is.na(tumor_data$smoking_status_simple) &
                          tumor_data$smoking_status_simple != "Not Reported" &
                          tumor_data$smoking_status_simple != "Unknown", ]

if(nrow(smoking_data) > 20) {
  p_smoking <- ggplot(smoking_data, aes(x = factor(smoking_status_simple),
                                       y = trmt10c_expression,
                                       fill = smoking_status_simple)) +
    geom_boxplot(alpha = 0.8, outlier.shape = 21, outlier.size = 1.2,
                 outlier.fill = "white", outlier.stroke = 0.5, width = 0.6) +
    geom_jitter(width = 0.15, alpha = 0.4, size = 0.8, shape = 16) +
    scale_fill_brewer(type = "qual", palette = "Set2") +
    scale_y_continuous(
      name = expression(bold("TRMT10C Expression (log"[2]*" TPM)")),
      labels = scales::comma_format()
    ) +
    scale_x_discrete(name = expression(bold("Smoking Status"))) +
    labs(
      title = expression(bold("TRMT10C Expression by Smoking Status")),
      subtitle = "Lung squamous cell carcinoma patients"
    ) +
    nature_theme() +
    guides(fill = "none") +
    stat_compare_means(method = "kruskal.test",
                      label.y = max(smoking_data$trmt10c_expression, na.rm = TRUE) * 1.15)

  # 添加样本数标注
  smoking_summary <- smoking_data %>%
    group_by(smoking_status_simple) %>%
    summarise(n = n(), .groups = 'drop')

  for(i in 1:nrow(smoking_summary)) {
    p_smoking <- p_smoking +
      annotate("text", x = i,
               y = max(smoking_data$trmt10c_expression, na.rm = TRUE) * 0.95,
               label = paste0("n=", smoking_summary$n[i]),
               size = 3, fontface = "bold")
  }

  ggsave("TRMT10C_Nature_Smoking_Status.png", p_smoking,
         width = 12, height = 8, dpi = 600, bg = "white")
  ggsave("TRMT10C_Nature_Smoking_Status.pdf", p_smoking,
         width = 12, height = 8, bg = "white")

  print("已保存: TRMT10C_Nature_Smoking_Status.png/pdf")
}

# 3. 吸烟量分析 (Pack-years)
print("=== 吸烟量分析 ===")

# 处理吸烟量数据
pack_years_data <- tumor_data[!is.na(tumor_data$pack_years_smoked), ]

if(nrow(pack_years_data) > 20) {
  # 按临床意义分组
  pack_years_data$smoking_intensity <- cut(pack_years_data$pack_years_smoked,
                                          breaks = c(0, 20, 40, Inf),
                                          labels = c("Light (<20)", "Moderate (20-40)", "Heavy (>40)"),
                                          include.lowest = TRUE)

  print("吸烟量分组分布:")
  print(table(pack_years_data$smoking_intensity, useNA = "always"))

  p_pack_years <- ggplot(pack_years_data, aes(x = smoking_intensity,
                                             y = trmt10c_expression,
                                             fill = smoking_intensity)) +
    geom_boxplot(alpha = 0.8, outlier.shape = 21, outlier.size = 1.2,
                 outlier.fill = "white", outlier.stroke = 0.5, width = 0.6) +
    geom_jitter(width = 0.15, alpha = 0.4, size = 0.8, shape = 16) +
    scale_fill_manual(values = c("Light (<20)" = "#66c2a5",
                                "Moderate (20-40)" = "#fc8d62",
                                "Heavy (>40)" = "#8da0cb")) +
    scale_y_continuous(
      name = expression(bold("TRMT10C Expression (log"[2]*" TPM)")),
      labels = scales::comma_format()
    ) +
    scale_x_discrete(name = expression(bold("Smoking Intensity (Pack-Years)"))) +
    labs(
      title = expression(bold("TRMT10C Expression by Smoking Intensity")),
      subtitle = "Dose-response relationship analysis"
    ) +
    nature_theme() +
    guides(fill = "none") +
    stat_compare_means(method = "kruskal.test",
                      label.y = max(pack_years_data$trmt10c_expression, na.rm = TRUE) * 1.15)

  ggsave("TRMT10C_Nature_Smoking_Intensity.png", p_pack_years,
         width = 10, height = 8, dpi = 600, bg = "white")
  ggsave("TRMT10C_Nature_Smoking_Intensity.pdf", p_pack_years,
         width = 10, height = 8, bg = "white")

  print("已保存: TRMT10C_Nature_Smoking_Intensity.png/pdf")
}

# 4. 详细TNM分期分析
print("=== 详细TNM分期分析 ===")

# T分期分析
t_stage_data <- tumor_data[!is.na(tumor_data$ajcc_pathologic_t), ]
if(nrow(t_stage_data) > 20) {
  print("T分期分布:")
  print(table(t_stage_data$ajcc_pathologic_t))

  p_t_stage <- ggplot(t_stage_data, aes(x = factor(ajcc_pathologic_t),
                                       y = trmt10c_expression,
                                       fill = ajcc_pathologic_t)) +
    geom_boxplot(alpha = 0.8, outlier.shape = 21, outlier.size = 1.2,
                 outlier.fill = "white", outlier.stroke = 0.5, width = 0.6) +
    geom_jitter(width = 0.15, alpha = 0.4, size = 0.8, shape = 16) +
    scale_fill_brewer(type = "seq", palette = "Reds") +
    scale_y_continuous(
      name = expression(bold("TRMT10C Expression (log"[2]*" TPM)")),
      labels = scales::comma_format()
    ) +
    scale_x_discrete(name = expression(bold("T Stage"))) +
    labs(
      title = expression(bold("TRMT10C Expression by T Stage")),
      subtitle = "Primary tumor size and invasion"
    ) +
    nature_theme() +
    guides(fill = "none") +
    stat_compare_means(method = "kruskal.test",
                      label.y = max(t_stage_data$trmt10c_expression, na.rm = TRUE) * 1.15)

  ggsave("TRMT10C_Nature_T_Stage.png", p_t_stage,
         width = 12, height = 8, dpi = 600, bg = "white")
  ggsave("TRMT10C_Nature_T_Stage.pdf", p_t_stage,
         width = 12, height = 8, bg = "white")

  print("已保存: TRMT10C_Nature_T_Stage.png/pdf")
}

# N分期分析
n_stage_data <- tumor_data[!is.na(tumor_data$ajcc_pathologic_n), ]
if(nrow(n_stage_data) > 20) {
  print("N分期分布:")
  print(table(n_stage_data$ajcc_pathologic_n))

  p_n_stage <- ggplot(n_stage_data, aes(x = factor(ajcc_pathologic_n),
                                       y = trmt10c_expression,
                                       fill = ajcc_pathologic_n)) +
    geom_boxplot(alpha = 0.8, outlier.shape = 21, outlier.size = 1.2,
                 outlier.fill = "white", outlier.stroke = 0.5, width = 0.6) +
    geom_jitter(width = 0.15, alpha = 0.4, size = 0.8, shape = 16) +
    scale_fill_brewer(type = "seq", palette = "Blues") +
    scale_y_continuous(
      name = expression(bold("TRMT10C Expression (log"[2]*" TPM)")),
      labels = scales::comma_format()
    ) +
    scale_x_discrete(name = expression(bold("N Stage"))) +
    labs(
      title = expression(bold("TRMT10C Expression by N Stage")),
      subtitle = "Regional lymph node involvement"
    ) +
    nature_theme() +
    guides(fill = "none") +
    stat_compare_means(method = "kruskal.test",
                      label.y = max(n_stage_data$trmt10c_expression, na.rm = TRUE) * 1.15)

  ggsave("TRMT10C_Nature_N_Stage.png", p_n_stage,
         width = 10, height = 8, dpi = 600, bg = "white")
  ggsave("TRMT10C_Nature_N_Stage.pdf", p_n_stage,
         width = 10, height = 8, bg = "white")

  print("已保存: TRMT10C_Nature_N_Stage.png/pdf")
}

# 5. 既往恶性肿瘤史分析
print("=== 既往恶性肿瘤史分析 ===")

prior_malignancy_data <- tumor_data[!is.na(tumor_data$prior_malignancy) &
                                   tumor_data$prior_malignancy != "not reported", ]

if(nrow(prior_malignancy_data) > 20) {
  print("既往恶性肿瘤史分布:")
  print(table(prior_malignancy_data$prior_malignancy))

  p_prior_malignancy <- ggplot(prior_malignancy_data,
                              aes(x = factor(prior_malignancy),
                                  y = trmt10c_expression,
                                  fill = prior_malignancy)) +
    geom_boxplot(alpha = 0.8, outlier.shape = 21, outlier.size = 1.2,
                 outlier.fill = "white", outlier.stroke = 0.5, width = 0.6) +
    geom_jitter(width = 0.15, alpha = 0.4, size = 0.8, shape = 16) +
    scale_fill_manual(values = c("no" = "#66c2a5", "yes" = "#fc8d62")) +
    scale_y_continuous(
      name = expression(bold("TRMT10C Expression (log"[2]*" TPM)")),
      labels = scales::comma_format()
    ) +
    scale_x_discrete(name = expression(bold("Prior Malignancy")),
                     labels = c("no" = "No", "yes" = "Yes")) +
    labs(
      title = expression(bold("TRMT10C Expression by Prior Malignancy History")),
      subtitle = "Comparison between patients with and without prior cancer"
    ) +
    nature_theme() +
    guides(fill = "none") +
    stat_compare_means(method = "wilcox.test",
                      label.y = max(prior_malignancy_data$trmt10c_expression, na.rm = TRUE) * 1.15)

  ggsave("TRMT10C_Nature_Prior_Malignancy.png", p_prior_malignancy,
         width = 8, height = 8, dpi = 600, bg = "white")
  ggsave("TRMT10C_Nature_Prior_Malignancy.pdf", p_prior_malignancy,
         width = 8, height = 8, bg = "white")

  print("已保存: TRMT10C_Nature_Prior_Malignancy.png/pdf")
}

# 6. 生存状态分析
print("=== 生存状态分析 ===")

vital_status_data <- tumor_data[!is.na(tumor_data$vital_status), ]

if(nrow(vital_status_data) > 20) {
  print("生存状态分布:")
  print(table(vital_status_data$vital_status))

  p_vital_status <- ggplot(vital_status_data,
                          aes(x = factor(vital_status),
                              y = trmt10c_expression,
                              fill = vital_status)) +
    geom_boxplot(alpha = 0.8, outlier.shape = 21, outlier.size = 1.2,
                 outlier.fill = "white", outlier.stroke = 0.5, width = 0.6) +
    geom_jitter(width = 0.15, alpha = 0.4, size = 0.8, shape = 16) +
    scale_fill_manual(values = c("Alive" = "#2E86AB", "Dead" = "#A23B72")) +
    scale_y_continuous(
      name = expression(bold("TRMT10C Expression (log"[2]*" TPM)")),
      labels = scales::comma_format()
    ) +
    scale_x_discrete(name = expression(bold("Vital Status"))) +
    labs(
      title = expression(bold("TRMT10C Expression by Vital Status")),
      subtitle = "Prognostic implications analysis"
    ) +
    nature_theme() +
    guides(fill = "none") +
    stat_compare_means(method = "wilcox.test",
                      label.y = max(vital_status_data$trmt10c_expression, na.rm = TRUE) * 1.15)

  ggsave("TRMT10C_Nature_Vital_Status.png", p_vital_status,
         width = 8, height = 8, dpi = 600, bg = "white")
  ggsave("TRMT10C_Nature_Vital_Status.pdf", p_vital_status,
         width = 8, height = 8, bg = "white")

  print("已保存: TRMT10C_Nature_Vital_Status.png/pdf")
}

# 7. 综合多维度分析 - 吸烟状态 vs 分期
print("=== 综合多维度分析 ===")

# 简化分期分组
tumor_data$stage_binary <- ifelse(tumor_data$ajcc_pathologic_stage %in% c("Stage I", "Stage IA", "Stage IB"),
                                  "Early (I)", "Advanced (II-IV)")

multi_dim_data <- tumor_data[!is.na(tumor_data$smoking_status_simple) &
                            !is.na(tumor_data$stage_binary) &
                            tumor_data$smoking_status_simple %in% c("Never", "Current", "Reformed ≤15y", "Reformed >15y"), ]

if(nrow(multi_dim_data) > 30) {
  print("多维度分析样本分布:")
  print(table(multi_dim_data$smoking_status_simple, multi_dim_data$stage_binary))

  p_multi_dim <- ggplot(multi_dim_data,
                       aes(x = smoking_status_simple,
                           y = trmt10c_expression,
                           fill = stage_binary)) +
    geom_boxplot(alpha = 0.8, outlier.shape = 21, outlier.size = 1,
                 outlier.fill = "white", outlier.stroke = 0.5,
                 position = position_dodge(width = 0.8), width = 0.6) +
    scale_fill_manual(values = c("Early (I)" = "#66c2a5", "Advanced (II-IV)" = "#fc8d62"),
                     name = "Stage") +
    scale_y_continuous(
      name = expression(bold("TRMT10C Expression (log"[2]*" TPM)")),
      labels = scales::comma_format()
    ) +
    scale_x_discrete(name = expression(bold("Smoking Status"))) +
    labs(
      title = expression(bold("TRMT10C Expression: Smoking Status vs Stage")),
      subtitle = "Multi-dimensional clinical subgroup analysis"
    ) +
    nature_theme() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
    guides(fill = guide_legend(override.aes = list(alpha = 1)))

  ggsave("TRMT10C_Nature_Multi_Dimensional.png", p_multi_dim,
         width = 12, height = 8, dpi = 600, bg = "white")
  ggsave("TRMT10C_Nature_Multi_Dimensional.pdf", p_multi_dim,
         width = 12, height = 8, bg = "white")

  print("已保存: TRMT10C_Nature_Multi_Dimensional.png/pdf")
}

# 8. 创建扩展亚组分析摘要
print("=== 生成扩展亚组分析摘要 ===")

# 统计摘要
extended_summary <- list(
  smoking_status = table(tumor_data$smoking_status_simple, useNA = "always"),
  pack_years = summary(tumor_data$pack_years_smoked),
  t_stage = table(tumor_data$ajcc_pathologic_t, useNA = "always"),
  n_stage = table(tumor_data$ajcc_pathologic_n, useNA = "always"),
  prior_malignancy = table(tumor_data$prior_malignancy, useNA = "always"),
  vital_status = table(tumor_data$vital_status, useNA = "always")
)

# 保存摘要
capture.output(extended_summary, file = "TRMT10C_Extended_Subgroup_Summary.txt")

print("=== 扩展亚组分析完成 ===")
print("新生成的Nature风格图表:")
print("- TRMT10C_Nature_Smoking_Status.png/pdf")
print("- TRMT10C_Nature_Smoking_Intensity.png/pdf")
print("- TRMT10C_Nature_T_Stage.png/pdf")
print("- TRMT10C_Nature_N_Stage.png/pdf")
print("- TRMT10C_Nature_Prior_Malignancy.png/pdf")
print("- TRMT10C_Nature_Vital_Status.png/pdf")
print("- TRMT10C_Nature_Multi_Dimensional.png/pdf")
print("")
print("分析摘要文件:")
print("- TRMT10C_Extended_Subgroup_Summary.txt")
print("")
print("特点:")
print("✓ 600 DPI高分辨率，适合期刊发表")
print("✓ Nature标准字体和颜色")
print("✓ 统计检验和样本数标注")
print("✓ 多维度临床相关亚组分析")
print("✓ 涵盖吸烟、TNM分期、既往病史、预后等维度")
