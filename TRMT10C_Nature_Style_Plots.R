# TRMT10C基因表达分析 - Nature/Lancet风格图表
# 高质量学术期刊标准可视化

setwd("C:/Users/<USER>/Desktop/code/TCGA/肺癌")

# 加载必要的包
library(TCGAbiolinks)
library(SummarizedExperiment)
library(ggplot2)
library(dplyr)
library(ggpubr)
library(ggsci)
library(cowplot)
library(scales)
library(RColorBrewer)

# 加载数据
load("TCGA_LUSC_RNAseq_hg38.rda")

# 数据预处理
rownames(data) <- rowData(data)$gene_name
data <- data[!duplicated(rownames(data)), ]
data <- data[rowData(data)$gene_type == "protein_coding", ]

exp_matrix <- assay(data, "unstranded")
samplesNT <- TCGAquery_SampleTypes(barcode = colnames(exp_matrix), typesample = c("NT"))
samplesTP <- TCGAquery_SampleTypes(barcode = colnames(exp_matrix), typesample = c("TP"))

# 获取临床数据
clinical <- GDCquery_clinic(project = "TCGA-LUSC", type = "clinical")
clinical$sample_id <- substr(clinical$submitter_id, 1, 12)

# 处理年龄分组
clinical$age_group <- cut(clinical$age_at_index,
                         breaks = c(0, 60, 70, 100),
                         labels = c("<60", "60-70", ">70"),
                         include.lowest = TRUE)

# 处理分期信息
clinical$stage_simple <- clinical$ajcc_pathologic_stage
clinical$stage_simple[clinical$stage_simple %in% c("Stage I", "Stage IA", "Stage IB")] <- "Stage I"
clinical$stage_simple[clinical$stage_simple %in% c("Stage II", "Stage IIA", "Stage IIB")] <- "Stage II"
clinical$stage_simple[clinical$stage_simple %in% c("Stage III", "Stage IIIA", "Stage IIIB")] <- "Stage III"
clinical$stage_simple[clinical$stage_simple == "Stage IV"] <- "Stage IV"

# 创建整合数据
trmt10c_expr <- exp_matrix["TRMT10C", ]
sample_info <- data.frame(
  sample_barcode = names(trmt10c_expr),
  trmt10c_expression = as.numeric(trmt10c_expr),
  sample_type = ifelse(names(trmt10c_expr) %in% samplesNT, "Normal", "Tumor"),
  stringsAsFactors = FALSE
)
sample_info$sample_id <- substr(sample_info$sample_barcode, 1, 12)
merged_data <- merge(sample_info, clinical, by = "sample_id", all.x = TRUE)

# Nature/Lancet风格主题设置
nature_theme <- function() {
  theme_classic() +
    theme(
      # 字体设置 - 使用系统默认字体
      text = element_text(color = "black"),

      # 标题设置
      plot.title = element_text(size = 14, face = "bold", hjust = 0.5,
                               margin = margin(b = 20)),
      plot.subtitle = element_text(size = 12, hjust = 0.5,
                                  margin = margin(b = 15)),

      # 坐标轴设置
      axis.title = element_text(size = 12, face = "bold", color = "black"),
      axis.text = element_text(size = 10, color = "black"),
      axis.line = element_line(color = "black", linewidth = 0.5),
      axis.ticks = element_line(color = "black", linewidth = 0.5),

      # 图例设置
      legend.title = element_text(size = 11, face = "bold"),
      legend.text = element_text(size = 10),
      legend.position = "bottom",
      legend.box.margin = margin(t = 10),

      # 面板设置
      panel.background = element_rect(fill = "white", color = NA),
      panel.grid = element_blank(),

      # 边距设置
      plot.margin = margin(20, 20, 20, 20),

      # 条带文本（用于分面图）
      strip.text = element_text(size = 11, face = "bold", color = "black"),
      strip.background = element_rect(fill = "grey95", color = "black", linewidth = 0.5)
    )
}

# Nature风格颜色方案
nature_colors <- list(
  # 主要对比色 - 蓝色(正常) vs 红色(肿瘤)
  sample_type = c("Normal" = "#2E86AB", "Tumor" = "#A23B72"),

  # 年龄分组 - 渐变蓝色系
  age_group = c("<60" = "#1f77b4", "60-70" = "#ff7f0e", ">70" = "#2ca02c"),

  # 分期 - 渐变色系
  stage = c("Stage I" = "#3498DB", "Stage II" = "#E74C3C",
           "Stage III" = "#F39C12", "Stage IV" = "#9B59B6"),

  # 性别
  gender = c("female" = "#E91E63", "male" = "#2196F3"),

  # 生存状态
  survival = c("Alive" = "#4CAF50", "Dead" = "#F44336")
)

# 统计标注函数
add_significance_stars <- function(p_value) {
  if (p_value < 0.001) return("***")
  else if (p_value < 0.01) return("**")
  else if (p_value < 0.05) return("*")
  else return("ns")
}

# 1. 基础对比图 - Nature风格
print("=== 生成Nature风格基础对比图 ===")

basic_data <- merged_data[!is.na(merged_data$trmt10c_expression), ]

# 计算统计检验
wilcox_result <- wilcox.test(
  basic_data$trmt10c_expression[basic_data$sample_type == "Normal"],
  basic_data$trmt10c_expression[basic_data$sample_type == "Tumor"]
)

# 计算样本数
sample_counts <- basic_data %>%
  group_by(sample_type) %>%
  summarise(
    n = n(),
    median_expr = median(trmt10c_expression, na.rm = TRUE),
    .groups = 'drop'
  )

p1_nature <- ggplot(basic_data, aes(x = sample_type, y = trmt10c_expression, fill = sample_type)) +
  # 箱线图 - 更细致的设置
  geom_boxplot(alpha = 0.8, outlier.shape = 21, outlier.size = 1.5,
               outlier.fill = "white", outlier.stroke = 0.5, width = 0.6) +

  # 小提琴图叠加（可选）
  # geom_violin(alpha = 0.3, width = 0.8) +

  # 散点图
  geom_jitter(width = 0.15, alpha = 0.4, size = 0.8, shape = 16) +

  # 颜色设置
  scale_fill_manual(values = nature_colors$sample_type) +
  scale_color_manual(values = nature_colors$sample_type) +

  # 坐标轴设置
  scale_y_continuous(
    name = expression(bold("TRMT10C Expression (log"[2]*" TPM)")),
    labels = comma_format(),
    expand = expansion(mult = c(0.05, 0.15))
  ) +

  scale_x_discrete(name = expression(bold("Sample Type"))) +

  # 标题
  labs(
    title = expression(bold("TRMT10C Expression in TCGA-LUSC")),
    subtitle = paste0("Normal (n=", sample_counts$n[1], ") vs Tumor (n=", sample_counts$n[2], ")")
  ) +

  # 应用主题
  nature_theme() +

  # 移除图例（因为x轴已经说明了）
  guides(fill = "none", color = "none") +

  # 添加统计标注
  annotate("text", x = 1.5, y = max(basic_data$trmt10c_expression) * 1.1,
           label = paste0("p = ", format(wilcox_result$p.value, scientific = TRUE, digits = 3)),
           size = 4, fontface = "bold") +

  annotate("text", x = 1.5, y = max(basic_data$trmt10c_expression) * 1.05,
           label = add_significance_stars(wilcox_result$p.value),
           size = 6, fontface = "bold")

# 保存高质量图片
ggsave("TRMT10C_Nature_Basic_Comparison.png", p1_nature,
       width = 6, height = 8, dpi = 600, bg = "white")
ggsave("TRMT10C_Nature_Basic_Comparison.pdf", p1_nature,
       width = 6, height = 8, bg = "white")

print("已保存: TRMT10C_Nature_Basic_Comparison.png/pdf")

# 2. 分期合并图 - Nature风格
print("=== 生成Nature风格分期合并图 ===")

stage_data <- merged_data[!is.na(merged_data$stage_simple) &
                         !is.na(merged_data$trmt10c_expression), ]

# 计算每个分期的样本数
stage_counts <- stage_data %>%
  group_by(stage_simple, sample_type) %>%
  summarise(n = n(), .groups = 'drop') %>%
  mutate(label = paste0("n=", n))

p2_nature <- ggplot(stage_data, aes(x = stage_simple, y = trmt10c_expression, fill = sample_type)) +
  # 箱线图
  geom_boxplot(alpha = 0.8, outlier.shape = 21, outlier.size = 1.2,
               outlier.fill = "white", outlier.stroke = 0.5,
               position = position_dodge(width = 0.8), width = 0.6) +

  # 散点图
  geom_point(aes(color = sample_type),
            position = position_jitterdodge(dodge.width = 0.8, jitter.width = 0.2),
            alpha = 0.5, size = 0.8, shape = 16) +

  # 颜色设置
  scale_fill_manual(values = nature_colors$sample_type, name = "Sample Type") +
  scale_color_manual(values = nature_colors$sample_type, name = "Sample Type") +

  # 坐标轴设置
  scale_y_continuous(
    name = expression(bold("TRMT10C Expression (log"[2]*" TPM)")),
    labels = comma_format(),
    expand = expansion(mult = c(0.05, 0.2))
  ) +

  scale_x_discrete(name = expression(bold("Pathological Stage"))) +

  # 标题
  labs(
    title = expression(bold("TRMT10C Expression by Pathological Stage")),
    subtitle = "Normal vs Tumor samples across different stages"
  ) +

  # 应用主题
  nature_theme() +

  # 图例设置
  guides(
    fill = guide_legend(override.aes = list(alpha = 1, size = 3)),
    color = "none"
  )

# 添加样本数标注
y_max <- max(stage_data$trmt10c_expression, na.rm = TRUE)
for (i in 1:nrow(stage_counts)) {
  stage_pos <- which(levels(factor(stage_data$stage_simple)) == stage_counts$stage_simple[i])
  x_pos <- stage_pos + ifelse(stage_counts$sample_type[i] == "Normal", -0.2, 0.2)

  p2_nature <- p2_nature +
    annotate("text", x = x_pos, y = y_max * 1.15,
             label = stage_counts$label[i], size = 3, fontface = "bold",
             color = nature_colors$sample_type[[stage_counts$sample_type[i]]])
}

# 保存
ggsave("TRMT10C_Nature_Stage_Combined.png", p2_nature,
       width = 10, height = 8, dpi = 600, bg = "white")
ggsave("TRMT10C_Nature_Stage_Combined.pdf", p2_nature,
       width = 10, height = 8, bg = "white")

print("已保存: TRMT10C_Nature_Stage_Combined.png/pdf")

# 3. 年龄分组合并图 - Nature风格
print("=== 生成Nature风格年龄分组合并图 ===")

age_data <- merged_data[!is.na(merged_data$age_group) &
                       !is.na(merged_data$trmt10c_expression), ]

# 计算统计检验
age_stats <- age_data %>%
  group_by(age_group) %>%
  summarise(
    p_value = wilcox.test(trmt10c_expression[sample_type == "Normal"],
                         trmt10c_expression[sample_type == "Tumor"])$p.value,
    .groups = 'drop'
  )

p3_nature <- ggplot(age_data, aes(x = age_group, y = trmt10c_expression, fill = sample_type)) +
  # 箱线图
  geom_boxplot(alpha = 0.8, outlier.shape = 21, outlier.size = 1.2,
               outlier.fill = "white", outlier.stroke = 0.5,
               position = position_dodge(width = 0.8), width = 0.6) +

  # 散点图
  geom_point(aes(color = sample_type),
            position = position_jitterdodge(dodge.width = 0.8, jitter.width = 0.2),
            alpha = 0.5, size = 0.8, shape = 16) +

  # 颜色设置
  scale_fill_manual(values = nature_colors$sample_type, name = "Sample Type") +
  scale_color_manual(values = nature_colors$sample_type, name = "Sample Type") +

  # 坐标轴设置
  scale_y_continuous(
    name = expression(bold("TRMT10C Expression (log"[2]*" TPM)")),
    labels = comma_format(),
    expand = expansion(mult = c(0.05, 0.2))
  ) +

  scale_x_discrete(name = expression(bold("Age Group (years)"))) +

  # 标题
  labs(
    title = expression(bold("TRMT10C Expression by Age Group")),
    subtitle = "Normal vs Tumor samples across different age groups"
  ) +

  # 应用主题
  nature_theme() +

  # 图例设置
  guides(
    fill = guide_legend(override.aes = list(alpha = 1, size = 3)),
    color = "none"
  )

# 添加统计显著性标注
y_max <- max(age_data$trmt10c_expression, na.rm = TRUE)
for (i in 1:nrow(age_stats)) {
  p3_nature <- p3_nature +
    annotate("text", x = i, y = y_max * 1.15,
             label = add_significance_stars(age_stats$p_value[i]),
             size = 5, fontface = "bold")
}

# 保存
ggsave("TRMT10C_Nature_Age_Combined.png", p3_nature,
       width = 10, height = 8, dpi = 600, bg = "white")
ggsave("TRMT10C_Nature_Age_Combined.pdf", p3_nature,
       width = 10, height = 8, bg = "white")

print("已保存: TRMT10C_Nature_Age_Combined.png/pdf")

# 4. 多面板组合图 - Nature风格
print("=== 生成Nature风格多面板组合图 ===")

# 创建分面数据
facet_data <- merged_data[!is.na(merged_data$stage_simple) &
                         !is.na(merged_data$age_group) &
                         !is.na(merged_data$trmt10c_expression), ]

# 创建分面图
p4_nature <- ggplot(facet_data, aes(x = sample_type, y = trmt10c_expression, fill = sample_type)) +
  # 箱线图
  geom_boxplot(alpha = 0.8, outlier.shape = 21, outlier.size = 1,
               outlier.fill = "white", outlier.stroke = 0.5, width = 0.6) +

  # 散点图
  geom_jitter(width = 0.15, alpha = 0.4, size = 0.6, shape = 16) +

  # 分面设置
  facet_grid(age_group ~ stage_simple,
             labeller = labeller(
               age_group = function(x) paste("Age:", x),
               stage_simple = function(x) x
             )) +

  # 颜色设置
  scale_fill_manual(values = nature_colors$sample_type) +

  # 坐标轴设置
  scale_y_continuous(
    name = expression(bold("TRMT10C Expression")),
    labels = comma_format()
  ) +

  scale_x_discrete(name = "", labels = c("N", "T")) +

  # 标题
  labs(
    title = expression(bold("TRMT10C Expression by Age Group and Stage")),
    subtitle = "Comprehensive analysis across clinical subgroups"
  ) +

  # 应用主题
  nature_theme() +

  # 特殊设置
  theme(
    axis.text.x = element_text(size = 9),
    strip.text = element_text(size = 10, face = "bold"),
    panel.spacing = unit(0.5, "lines")
  ) +

  # 移除图例
  guides(fill = "none")

# 保存
ggsave("TRMT10C_Nature_Facet_Combined.png", p4_nature,
       width = 12, height = 10, dpi = 600, bg = "white")
ggsave("TRMT10C_Nature_Facet_Combined.pdf", p4_nature,
       width = 12, height = 10, bg = "white")

print("已保存: TRMT10C_Nature_Facet_Combined.png/pdf")

# 5. 创建Figure组合 - 类似Nature多面板图
print("=== 生成Nature风格Figure组合 ===")

# 使用cowplot创建组合图
figure_combined <- plot_grid(
  p1_nature + theme(plot.margin = margin(10, 10, 10, 10)),
  p2_nature + theme(plot.margin = margin(10, 10, 10, 10)),
  p3_nature + theme(plot.margin = margin(10, 10, 10, 10)),
  p4_nature + theme(plot.margin = margin(10, 10, 10, 10)),
  labels = c("A", "B", "C", "D"),
  label_size = 16,
  label_fontface = "bold",
  ncol = 2,
  nrow = 2,
  align = "hv",
  axis = "tblr"
)

# 保存组合图
ggsave("TRMT10C_Nature_Figure_Combined.png", figure_combined,
       width = 16, height = 14, dpi = 600, bg = "white")
ggsave("TRMT10C_Nature_Figure_Combined.pdf", figure_combined,
       width = 16, height = 14, bg = "white")

print("已保存: TRMT10C_Nature_Figure_Combined.png/pdf")

# 6. 生成统计摘要表
print("=== 生成统计摘要 ===")

# 基础统计
basic_stats <- merged_data %>%
  filter(!is.na(trmt10c_expression)) %>%
  group_by(sample_type) %>%
  summarise(
    n = n(),
    mean = round(mean(trmt10c_expression, na.rm = TRUE), 2),
    median = round(median(trmt10c_expression, na.rm = TRUE), 2),
    sd = round(sd(trmt10c_expression, na.rm = TRUE), 2),
    q25 = round(quantile(trmt10c_expression, 0.25, na.rm = TRUE), 2),
    q75 = round(quantile(trmt10c_expression, 0.75, na.rm = TRUE), 2),
    .groups = 'drop'
  )

# 分期统计
stage_stats <- merged_data %>%
  filter(!is.na(trmt10c_expression) & !is.na(stage_simple)) %>%
  group_by(stage_simple, sample_type) %>%
  summarise(
    n = n(),
    median = round(median(trmt10c_expression, na.rm = TRUE), 2),
    .groups = 'drop'
  )

# 年龄统计
age_stats_detailed <- merged_data %>%
  filter(!is.na(trmt10c_expression) & !is.na(age_group)) %>%
  group_by(age_group, sample_type) %>%
  summarise(
    n = n(),
    median = round(median(trmt10c_expression, na.rm = TRUE), 2),
    .groups = 'drop'
  )

# 保存统计表
write.csv(basic_stats, "TRMT10C_Nature_Basic_Statistics.csv", row.names = FALSE)
write.csv(stage_stats, "TRMT10C_Nature_Stage_Statistics.csv", row.names = FALSE)
write.csv(age_stats_detailed, "TRMT10C_Nature_Age_Statistics.csv", row.names = FALSE)

print("=== Nature风格图表生成完成 ===")
print("生成的文件:")
print("高质量图片 (PNG + PDF):")
print("- TRMT10C_Nature_Basic_Comparison.png/pdf")
print("- TRMT10C_Nature_Stage_Combined.png/pdf")
print("- TRMT10C_Nature_Age_Combined.png/pdf")
print("- TRMT10C_Nature_Facet_Combined.png/pdf")
print("- TRMT10C_Nature_Figure_Combined.png/pdf (多面板组合)")
print("")
print("统计表格:")
print("- TRMT10C_Nature_Basic_Statistics.csv")
print("- TRMT10C_Nature_Stage_Statistics.csv")
print("- TRMT10C_Nature_Age_Statistics.csv")
print("")
print("特点:")
print("✓ 600 DPI高分辨率，适合期刊发表")
print("✓ Nature/Lancet标准字体和颜色")
print("✓ 专业的统计标注和样本数显示")
print("✓ 同时提供PNG和PDF格式")
print("✓ 多面板组合图，类似Nature Figure")
