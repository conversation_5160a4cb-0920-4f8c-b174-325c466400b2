# TRMT10C基因在TCGA LUSC中的表达差异分析
# 包含正常vs肿瘤，以及肿瘤内多个亚组分析

setwd("C:/Users/<USER>/Desktop/code/TCGA/肺癌")

# 加载必要的包
library(TCGAbiolinks)
library(SummarizedExperiment)
library(ggplot2)
library(dplyr)
library(reshape2)
library(RColorBrewer)

# 检查并安装ggpubr包
if (!require(ggpubr, quietly = TRUE)) {
  install.packages("ggpubr")
  library(ggpubr)
}

# 1. 加载和预处理数据
print("=== 加载数据 ===")
load("TCGA_LUSC_RNAseq_hg38.rda")

# 处理表达数据
rownames(data) <- rowData(data)$gene_name
data <- data[!duplicated(rownames(data)),]
data <- data[rowData(data)$gene_type=="protein_coding",]

# 获取表达矩阵
exp_matrix <- assay(data, "unstranded")
print(paste("表达矩阵维度:", nrow(exp_matrix), "x", ncol(exp_matrix)))

# 检查TRMT10C
if(!"TRMT10C" %in% rownames(exp_matrix)) {
  stop("TRMT10C基因不存在于数据中")
}

# 获取样本类型
samplesNT <- TCGAquery_SampleTypes(barcode = colnames(exp_matrix), typesample = c("NT"))
samplesTP <- TCGAquery_SampleTypes(barcode = colnames(exp_matrix), typesample = c("TP"))

print(paste("正常样本数:", length(samplesNT)))
print(paste("肿瘤样本数:", length(samplesTP)))

# 2. 获取和处理临床数据
print("=== 处理临床数据 ===")
clinical <- GDCquery_clinic(project = "TCGA-LUSC", type = "clinical")

# 提取样本ID（前12位）
clinical$sample_id <- substr(clinical$submitter_id, 1, 12)

# 处理年龄分组
clinical$age_group <- cut(clinical$age_at_index,
                         breaks = c(0, 60, 70, 100),
                         labels = c("<60", "60-70", ">70"),
                         include.lowest = TRUE)

# 处理分期信息
clinical$stage_simple <- clinical$ajcc_pathologic_stage
clinical$stage_simple[clinical$stage_simple %in% c("Stage I", "Stage IA", "Stage IB")] <- "Stage I"
clinical$stage_simple[clinical$stage_simple %in% c("Stage II", "Stage IIA", "Stage IIB")] <- "Stage II"
clinical$stage_simple[clinical$stage_simple %in% c("Stage III", "Stage IIIA", "Stage IIIB")] <- "Stage III"
clinical$stage_simple[clinical$stage_simple == "Stage IV"] <- "Stage IV"

# 处理生存状态
clinical$survival_status <- clinical$vital_status

# 3. 创建整合数据框
print("=== 创建整合数据 ===")

# 获取TRMT10C表达数据
trmt10c_expr <- exp_matrix["TRMT10C", ]

# 创建样本信息数据框
sample_info <- data.frame(
  sample_barcode = names(trmt10c_expr),
  trmt10c_expression = as.numeric(trmt10c_expr),
  sample_type = ifelse(names(trmt10c_expr) %in% samplesNT, "Normal", "Tumor"),
  stringsAsFactors = FALSE
)

# 提取样本ID用于匹配临床数据
sample_info$sample_id <- substr(sample_info$sample_barcode, 1, 12)

# 合并临床数据
merged_data <- merge(sample_info, clinical, by = "sample_id", all.x = TRUE)

# 确保数据框格式正确
merged_data <- as.data.frame(merged_data)

# 只保留肿瘤样本用于亚组分析
tumor_data <- merged_data[merged_data$sample_type == "Tumor", ]
tumor_data <- as.data.frame(tumor_data)

print(paste("合并后数据维度:", nrow(merged_data)))
print(paste("肿瘤样本数据维度:", nrow(tumor_data)))

# 检查数据结构
print("合并数据的列名:")
print(colnames(merged_data))
print("数据类型检查:")
print(sapply(merged_data[1:5], class))

# 4. 保存整合数据到TSV文件
print("=== 保存数据到TSV文件 ===")

# 选择要保存的关键列，避免复杂的列表类型
key_columns <- c("sample_id", "sample_barcode", "trmt10c_expression", "sample_type",
                "age_at_index", "age_group", "gender", "race", "vital_status",
                "ajcc_pathologic_stage", "stage_simple", "survival_status",
                "days_to_death", "days_to_last_follow_up")

# 确保列存在
available_columns <- key_columns[key_columns %in% colnames(merged_data)]
print("可用的关键列:")
print(available_columns)

# 创建简化的数据框
merged_data_simple <- merged_data[, available_columns, drop = FALSE]
tumor_data_simple <- tumor_data[, available_columns, drop = FALSE]

# 转换为字符型以避免复杂数据类型问题
for(col in colnames(merged_data_simple)) {
  if(!is.numeric(merged_data_simple[[col]])) {
    merged_data_simple[[col]] <- as.character(merged_data_simple[[col]])
  }
}

for(col in colnames(tumor_data_simple)) {
  if(!is.numeric(tumor_data_simple[[col]])) {
    tumor_data_simple[[col]] <- as.character(tumor_data_simple[[col]])
  }
}

# 保存完整数据
write.table(merged_data_simple, "TRMT10C_integrated_data.tsv",
           sep = "\t", row.names = FALSE, quote = FALSE)

# 保存肿瘤数据
write.table(tumor_data_simple, "TRMT10C_tumor_data.tsv",
           sep = "\t", row.names = FALSE, quote = FALSE)

# 保存表达矩阵（仅TRMT10C）
trmt10c_matrix <- data.frame(
  sample_barcode = names(trmt10c_expr),
  TRMT10C = as.numeric(trmt10c_expr)
)
write.table(trmt10c_matrix, "TRMT10C_expression_matrix.tsv",
           sep = "\t", row.names = FALSE, quote = FALSE)

print("数据已保存到以下TSV文件:")
print("- TRMT10C_integrated_data.tsv (完整数据)")
print("- TRMT10C_tumor_data.tsv (仅肿瘤样本)")
print("- TRMT10C_expression_matrix.tsv (表达矩阵)")

# 5. 数据质量检查
print("=== 数据质量检查 ===")
print("正常vs肿瘤样本TRMT10C表达:")
normal_expr <- merged_data$trmt10c_expression[merged_data$sample_type == "Normal"]
tumor_expr <- merged_data$trmt10c_expression[merged_data$sample_type == "Tumor"]

print(paste("正常样本表达 - 中位数:", round(median(normal_expr, na.rm = TRUE), 2)))
print(paste("肿瘤样本表达 - 中位数:", round(median(tumor_expr, na.rm = TRUE), 2)))

# 统计检验
wilcox_test <- wilcox.test(normal_expr, tumor_expr)
print(paste("Wilcoxon检验 p值:", format(wilcox_test$p.value, scientific = TRUE)))

# 检查各亚组的样本数
print("=== 亚组样本数统计 ===")
print("年龄分组:")
print(table(tumor_data$age_group, useNA = "always"))

print("分期分组:")
print(table(tumor_data$stage_simple, useNA = "always"))

print("性别分组:")
print(table(tumor_data$gender, useNA = "always"))

print("人种分组:")
print(table(tumor_data$race, useNA = "always"))

print("生存状态:")
print(table(tumor_data$survival_status, useNA = "always"))

print("=== 数据预处理完成 ===")
print("接下来将生成可视化图表...")

# 6. 可视化函数定义
create_boxplot <- function(data, x_var, y_var, title, x_label, y_label,
                          fill_colors = NULL, stat_test = TRUE) {
  p <- ggplot(data, aes_string(x = x_var, y = y_var, fill = x_var)) +
    geom_boxplot(alpha = 0.7, outlier.shape = 16, outlier.size = 1) +
    geom_jitter(width = 0.2, alpha = 0.5, size = 0.8) +
    labs(title = title, x = x_label, y = y_label) +
    theme_classic() +
    theme(
      plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
      axis.text.x = element_text(angle = 45, hjust = 1),
      legend.position = "none"
    )

  if (!is.null(fill_colors)) {
    p <- p + scale_fill_manual(values = fill_colors)
  }

  if (stat_test && length(unique(data[[x_var]])) == 2) {
    p <- p + stat_compare_means(method = "wilcox.test",
                               label.y = max(data[[y_var]], na.rm = TRUE) * 1.1)
  } else if (stat_test && length(unique(data[[x_var]])) > 2) {
    p <- p + stat_compare_means(method = "kruskal.test",
                               label.y = max(data[[y_var]], na.rm = TRUE) * 1.1)
  }

  return(p)
}

# 7. 基础差异分析图：正常 vs 肿瘤
print("=== 生成基础差异分析图 ===")

# 正常vs肿瘤箱线图
p1 <- create_boxplot(
  data = merged_data[!is.na(merged_data$trmt10c_expression), ],
  x_var = "sample_type",
  y_var = "trmt10c_expression",
  title = "TRMT10C Expression: Normal vs Tumor",
  x_label = "Sample Type",
  y_label = "TRMT10C Expression",
  fill_colors = c("Normal" = "#3498db", "Tumor" = "#e74c3c")
)

ggsave("TRMT10C_Normal_vs_Tumor.png", p1, width = 8, height = 6, dpi = 300)
print("已保存: TRMT10C_Normal_vs_Tumor.png")

# 8. 按分期分别生成图表
print("=== 按分期生成图表 ===")

stages <- c("Stage I", "Stage II", "Stage III", "Stage IV")

for (stage in stages) {
  stage_data <- tumor_data[tumor_data$stage_simple == stage &
                          !is.na(tumor_data$stage_simple), ]

  if (nrow(stage_data) < 10) {
    print(paste("跳过", stage, "- 样本数不足:", nrow(stage_data)))
    next
  }

  print(paste("处理", stage, "- 样本数:", nrow(stage_data)))

  # 年龄分组图
  if (sum(!is.na(stage_data$age_group)) >= 10) {
    p_age <- create_boxplot(
      data = stage_data[!is.na(stage_data$age_group), ],
      x_var = "age_group",
      y_var = "trmt10c_expression",
      title = paste("TRMT10C Expression by Age Group -", stage),
      x_label = "Age Group",
      y_label = "TRMT10C Expression",
      fill_colors = c("<60" = "#2ecc71", "60-70" = "#f39c12", ">70" = "#9b59b6")
    )

    filename <- paste0("TRMT10C_", gsub(" ", "_", stage), "_Age_Groups.png")
    ggsave(filename, p_age, width = 10, height = 6, dpi = 300)
    print(paste("已保存:", filename))
  }

  # 性别分组图
  if (sum(!is.na(stage_data$gender)) >= 10) {
    p_gender <- create_boxplot(
      data = stage_data[!is.na(stage_data$gender), ],
      x_var = "gender",
      y_var = "trmt10c_expression",
      title = paste("TRMT10C Expression by Gender -", stage),
      x_label = "Gender",
      y_label = "TRMT10C Expression",
      fill_colors = c("female" = "#e91e63", "male" = "#2196f3")
    )

    filename <- paste0("TRMT10C_", gsub(" ", "_", stage), "_Gender.png")
    ggsave(filename, p_gender, width = 8, height = 6, dpi = 300)
    print(paste("已保存:", filename))
  }

  # 生存状态分组图
  if (sum(!is.na(stage_data$survival_status)) >= 10) {
    p_survival <- create_boxplot(
      data = stage_data[!is.na(stage_data$survival_status), ],
      x_var = "survival_status",
      y_var = "trmt10c_expression",
      title = paste("TRMT10C Expression by Survival Status -", stage),
      x_label = "Survival Status",
      y_label = "TRMT10C Expression",
      fill_colors = c("Alive" = "#4caf50", "Dead" = "#f44336")
    )

    filename <- paste0("TRMT10C_", gsub(" ", "_", stage), "_Survival.png")
    ggsave(filename, p_survival, width = 8, height = 6, dpi = 300)
    print(paste("已保存:", filename))
  }
}

# 9. 综合分析图：所有肿瘤样本的亚组分析
print("=== 生成综合亚组分析图 ===")

# 所有肿瘤样本的年龄分组分析
if (sum(!is.na(tumor_data$age_group)) >= 20) {
  p_all_age <- create_boxplot(
    data = tumor_data[!is.na(tumor_data$age_group), ],
    x_var = "age_group",
    y_var = "trmt10c_expression",
    title = "TRMT10C Expression by Age Group (All Tumor Samples)",
    x_label = "Age Group",
    y_label = "TRMT10C Expression",
    fill_colors = c("<60" = "#2ecc71", "60-70" = "#f39c12", ">70" = "#9b59b6")
  )

  ggsave("TRMT10C_All_Tumors_Age_Groups.png", p_all_age, width = 10, height = 6, dpi = 300)
  print("已保存: TRMT10C_All_Tumors_Age_Groups.png")
}

# 所有肿瘤样本的性别分析
if (sum(!is.na(tumor_data$gender)) >= 20) {
  p_all_gender <- create_boxplot(
    data = tumor_data[!is.na(tumor_data$gender), ],
    x_var = "gender",
    y_var = "trmt10c_expression",
    title = "TRMT10C Expression by Gender (All Tumor Samples)",
    x_label = "Gender",
    y_label = "TRMT10C Expression",
    fill_colors = c("female" = "#e91e63", "male" = "#2196f3")
  )

  ggsave("TRMT10C_All_Tumors_Gender.png", p_all_gender, width = 8, height = 6, dpi = 300)
  print("已保存: TRMT10C_All_Tumors_Gender.png")
}

# 所有肿瘤样本的生存状态分析
if (sum(!is.na(tumor_data$survival_status)) >= 20) {
  p_all_survival <- create_boxplot(
    data = tumor_data[!is.na(tumor_data$survival_status), ],
    x_var = "survival_status",
    y_var = "trmt10c_expression",
    title = "TRMT10C Expression by Survival Status (All Tumor Samples)",
    x_label = "Survival Status",
    y_label = "TRMT10C Expression",
    fill_colors = c("Alive" = "#4caf50", "Dead" = "#f44336")
  )

  ggsave("TRMT10C_All_Tumors_Survival.png", p_all_survival, width = 8, height = 6, dpi = 300)
  print("已保存: TRMT10C_All_Tumors_Survival.png")
}

# 所有肿瘤样本的分期分析
if (sum(!is.na(tumor_data$stage_simple)) >= 20) {
  p_all_stage <- create_boxplot(
    data = tumor_data[!is.na(tumor_data$stage_simple), ],
    x_var = "stage_simple",
    y_var = "trmt10c_expression",
    title = "TRMT10C Expression by Stage (All Tumor Samples)",
    x_label = "Stage",
    y_label = "TRMT10C Expression",
    fill_colors = c("Stage I" = "#3498db", "Stage II" = "#2ecc71",
                    "Stage III" = "#f39c12", "Stage IV" = "#e74c3c")
  )

  ggsave("TRMT10C_All_Tumors_Stage.png", p_all_stage, width = 10, height = 6, dpi = 300)
  print("已保存: TRMT10C_All_Tumors_Stage.png")
}

# 10. 生成分析报告
print("=== 生成分析报告 ===")

# 统计分析结果
analysis_results <- list()

# 正常vs肿瘤
normal_expr <- merged_data$trmt10c_expression[merged_data$sample_type == "Normal"]
tumor_expr <- merged_data$trmt10c_expression[merged_data$sample_type == "Tumor"]
wilcox_result <- wilcox.test(normal_expr, tumor_expr)

analysis_results$normal_vs_tumor <- list(
  normal_median = median(normal_expr, na.rm = TRUE),
  tumor_median = median(tumor_expr, na.rm = TRUE),
  p_value = wilcox_result$p.value,
  effect_size = median(tumor_expr, na.rm = TRUE) / median(normal_expr, na.rm = TRUE)
)

# 保存分析结果
save(analysis_results, file = "TRMT10C_analysis_results.rda")

print("=== 分析完成 ===")
print("生成的文件:")
print("数据文件:")
print("- TRMT10C_integrated_data.tsv")
print("- TRMT10C_tumor_data.tsv")
print("- TRMT10C_expression_matrix.tsv")
print("- TRMT10C_analysis_results.rda")
print("")
print("图表文件:")
print("- TRMT10C_Normal_vs_Tumor.png")
print("- TRMT10C_All_Tumors_*.png (综合分析)")
print("- TRMT10C_Stage_*_*.png (按分期分析)")
print("")
print("分析总结:")
print(paste("正常样本TRMT10C表达中位数:", round(analysis_results$normal_vs_tumor$normal_median, 2)))
print(paste("肿瘤样本TRMT10C表达中位数:", round(analysis_results$normal_vs_tumor$tumor_median, 2)))
print(paste("Wilcoxon检验p值:", format(analysis_results$normal_vs_tumor$p_value, scientific = TRUE)))
print(paste("效应大小(肿瘤/正常):", round(analysis_results$normal_vs_tumor$effect_size, 3)))
