# TRMT10C基因在TCGA LUSC中的表达差异分析
# 包含正常vs肿瘤，以及肿瘤内多个亚组分析

setwd("C:/Users/<USER>/Desktop/code/TCGA/肺癌")

# 加载必要的包
library(TCGAbiolinks)
library(SummarizedExperiment)
library(ggplot2)
library(dplyr)
library(reshape2)
library(RColorBrewer)

# 检查并安装ggpubr包
if (!require(ggpubr, quietly = TRUE)) {
  install.packages("ggpubr")
  library(ggpubr)
}

# 1. 加载和预处理数据
print("=== 加载数据 ===")
load("TCGA_LUSC_RNAseq_hg38.rda")

# 处理表达数据
rownames(data) <- rowData(data)$gene_name
data <- data[!duplicated(rownames(data)),]
data <- data[rowData(data)$gene_type=="protein_coding",]

# 获取表达矩阵
exp_matrix <- assay(data, "unstranded")
print(paste("表达矩阵维度:", nrow(exp_matrix), "x", ncol(exp_matrix)))

# 检查TRMT10C
if(!"TRMT10C" %in% rownames(exp_matrix)) {
  stop("TRMT10C基因不存在于数据中")
}

# 获取样本类型
samplesNT <- TCGAquery_SampleTypes(barcode = colnames(exp_matrix), typesample = c("NT"))
samplesTP <- TCGAquery_SampleTypes(barcode = colnames(exp_matrix), typesample = c("TP"))

print(paste("正常样本数:", length(samplesNT)))
print(paste("肿瘤样本数:", length(samplesTP)))

# 2. 获取和处理临床数据
print("=== 处理临床数据 ===")
clinical <- GDCquery_clinic(project = "TCGA-LUSC", type = "clinical")

# 提取样本ID（前12位）
clinical$sample_id <- substr(clinical$submitter_id, 1, 12)

# 处理年龄分组
clinical$age_group <- cut(clinical$age_at_index,
                         breaks = c(0, 60, 70, 100),
                         labels = c("<60", "60-70", ">70"),
                         include.lowest = TRUE)

# 处理分期信息
clinical$stage_simple <- clinical$ajcc_pathologic_stage
clinical$stage_simple[clinical$stage_simple %in% c("Stage I", "Stage IA", "Stage IB")] <- "Stage I"
clinical$stage_simple[clinical$stage_simple %in% c("Stage II", "Stage IIA", "Stage IIB")] <- "Stage II"
clinical$stage_simple[clinical$stage_simple %in% c("Stage III", "Stage IIIA", "Stage IIIB")] <- "Stage III"
clinical$stage_simple[clinical$stage_simple == "Stage IV"] <- "Stage IV"

# 处理生存状态
clinical$survival_status <- clinical$vital_status

# 3. 创建整合数据框
print("=== 创建整合数据 ===")

# 获取TRMT10C表达数据
trmt10c_expr <- exp_matrix["TRMT10C", ]

# 创建样本信息数据框
sample_info <- data.frame(
  sample_barcode = names(trmt10c_expr),
  trmt10c_expression = as.numeric(trmt10c_expr),
  sample_type = ifelse(names(trmt10c_expr) %in% samplesNT, "Normal", "Tumor"),
  stringsAsFactors = FALSE
)

# 提取样本ID用于匹配临床数据
sample_info$sample_id <- substr(sample_info$sample_barcode, 1, 12)

# 合并临床数据
merged_data <- merge(sample_info, clinical, by = "sample_id", all.x = TRUE)

# 确保数据框格式正确
merged_data <- as.data.frame(merged_data)

# 只保留肿瘤样本用于亚组分析
tumor_data <- merged_data[merged_data$sample_type == "Tumor", ]
tumor_data <- as.data.frame(tumor_data)

print(paste("合并后数据维度:", nrow(merged_data)))
print(paste("肿瘤样本数据维度:", nrow(tumor_data)))

# 检查数据结构
print("合并数据的列名:")
print(colnames(merged_data))
print("数据类型检查:")
print(sapply(merged_data[1:5], class))

# 4. 保存整合数据到TSV文件
print("=== 保存数据到TSV文件 ===")

# 选择要保存的关键列，避免复杂的列表类型
key_columns <- c("sample_id", "sample_barcode", "trmt10c_expression", "sample_type",
                "age_at_index", "age_group", "gender", "race", "vital_status",
                "ajcc_pathologic_stage", "stage_simple", "survival_status",
                "days_to_death", "days_to_last_follow_up")

# 确保列存在
available_columns <- key_columns[key_columns %in% colnames(merged_data)]
print("可用的关键列:")
print(available_columns)

# 创建简化的数据框
merged_data_simple <- merged_data[, available_columns, drop = FALSE]
tumor_data_simple <- tumor_data[, available_columns, drop = FALSE]

# 转换为字符型以避免复杂数据类型问题
for(col in colnames(merged_data_simple)) {
  if(!is.numeric(merged_data_simple[[col]])) {
    merged_data_simple[[col]] <- as.character(merged_data_simple[[col]])
  }
}

for(col in colnames(tumor_data_simple)) {
  if(!is.numeric(tumor_data_simple[[col]])) {
    tumor_data_simple[[col]] <- as.character(tumor_data_simple[[col]])
  }
}

# 保存完整数据
write.table(merged_data_simple, "TRMT10C_integrated_data.tsv",
           sep = "\t", row.names = FALSE, quote = FALSE)

# 保存肿瘤数据
write.table(tumor_data_simple, "TRMT10C_tumor_data.tsv",
           sep = "\t", row.names = FALSE, quote = FALSE)

# 保存表达矩阵（仅TRMT10C）
trmt10c_matrix <- data.frame(
  sample_barcode = names(trmt10c_expr),
  TRMT10C = as.numeric(trmt10c_expr)
)
write.table(trmt10c_matrix, "TRMT10C_expression_matrix.tsv",
           sep = "\t", row.names = FALSE, quote = FALSE)

print("数据已保存到以下TSV文件:")
print("- TRMT10C_integrated_data.tsv (完整数据)")
print("- TRMT10C_tumor_data.tsv (仅肿瘤样本)")
print("- TRMT10C_expression_matrix.tsv (表达矩阵)")

# 5. 数据质量检查
print("=== 数据质量检查 ===")
print("正常vs肿瘤样本TRMT10C表达:")
normal_expr <- merged_data$trmt10c_expression[merged_data$sample_type == "Normal"]
tumor_expr <- merged_data$trmt10c_expression[merged_data$sample_type == "Tumor"]

print(paste("正常样本表达 - 中位数:", round(median(normal_expr, na.rm = TRUE), 2)))
print(paste("肿瘤样本表达 - 中位数:", round(median(tumor_expr, na.rm = TRUE), 2)))

# 统计检验
wilcox_test <- wilcox.test(normal_expr, tumor_expr)
print(paste("Wilcoxon检验 p值:", format(wilcox_test$p.value, scientific = TRUE)))

# 检查各亚组的样本数
print("=== 亚组样本数统计 ===")
print("年龄分组:")
print(table(tumor_data$age_group, useNA = "always"))

print("分期分组:")
print(table(tumor_data$stage_simple, useNA = "always"))

print("性别分组:")
print(table(tumor_data$gender, useNA = "always"))

print("人种分组:")
print(table(tumor_data$race, useNA = "always"))

print("生存状态:")
print(table(tumor_data$survival_status, useNA = "always"))

print("=== 数据预处理完成 ===")
print("接下来将生成可视化图表...")

# 6. 可视化函数定义 - 方案1：每个亚组包含正常vs肿瘤对比
create_subgroup_boxplot <- function(data, subgroup_var, title_prefix,
                                   min_normal_samples = 2) {

  # 获取所有亚组
  subgroups <- unique(data[[subgroup_var]])
  subgroups <- subgroups[!is.na(subgroups)]

  plots_list <- list()

  for (subgroup in subgroups) {
    # 筛选当前亚组的数据
    subgroup_data <- data[data[[subgroup_var]] == subgroup &
                         !is.na(data[[subgroup_var]]), ]

    # 检查正常和肿瘤样本数
    normal_count <- sum(subgroup_data$sample_type == "Normal", na.rm = TRUE)
    tumor_count <- sum(subgroup_data$sample_type == "Tumor", na.rm = TRUE)

    # 如果样本数太少，跳过
    if (normal_count == 0 || tumor_count == 0) {
      print(paste("跳过", subgroup, "- 缺少正常或肿瘤样本"))
      next
    }

    # 创建标题，包含样本数信息
    if (normal_count < min_normal_samples) {
      plot_title <- paste0(title_prefix, " - ", subgroup,
                          "\n(Normal: ", normal_count, ", Tumor: ", tumor_count,
                          " - 正常样本数不足)")
    } else {
      plot_title <- paste0(title_prefix, " - ", subgroup,
                          "\n(Normal: ", normal_count, ", Tumor: ", tumor_count, ")")
    }

    # 创建图表
    p <- ggplot(subgroup_data, aes(x = sample_type, y = trmt10c_expression,
                                  fill = sample_type)) +
      geom_boxplot(alpha = 0.7, outlier.shape = 16, outlier.size = 1) +
      geom_jitter(width = 0.2, alpha = 0.6, size = 1) +
      scale_fill_manual(values = c("Normal" = "#3498db", "Tumor" = "#e74c3c")) +
      labs(title = plot_title,
           x = "Sample Type",
           y = "TRMT10C Expression") +
      theme_classic() +
      theme(
        plot.title = element_text(hjust = 0.5, size = 12, face = "bold"),
        axis.text.x = element_text(size = 10),
        legend.position = "none"
      )

    # 添加统计检验（如果正常样本足够）
    if (normal_count >= min_normal_samples) {
      p <- p + stat_compare_means(method = "wilcox.test",
                                 label.y = max(subgroup_data$trmt10c_expression, na.rm = TRUE) * 1.1)
    } else {
      # 样本不足时添加警告文本
      p <- p + annotate("text", x = 1.5,
                       y = max(subgroup_data$trmt10c_expression, na.rm = TRUE) * 1.1,
                       label = "样本数不足", color = "red", size = 3)
    }

    plots_list[[subgroup]] <- p
  }

  return(plots_list)
}

# 创建综合分组图的函数
create_comprehensive_boxplot <- function(data, group_var, title,
                                       color_palette = NULL) {

  # 创建分组变量，结合样本类型和亚组
  data$combined_group <- paste(data[[group_var]], data$sample_type, sep = "_")

  # 移除NA值
  plot_data <- data[!is.na(data[[group_var]]) & !is.na(data$trmt10c_expression), ]

  if (nrow(plot_data) == 0) {
    print(paste("警告: 没有有效数据用于", title))
    return(NULL)
  }

  # 设置默认颜色
  if (is.null(color_palette)) {
    groups <- unique(plot_data[[group_var]])
    n_groups <- length(groups)
    base_colors <- RColorBrewer::brewer.pal(min(n_groups, 8), "Set2")

    color_palette <- c()
    for (i in 1:n_groups) {
      # 为每个组设置正常（浅色）和肿瘤（深色）
      normal_color <- paste0(base_colors[i], "80")  # 添加透明度
      tumor_color <- base_colors[i]
      color_palette[paste0(groups[i], "_Normal")] <- normal_color
      color_palette[paste0(groups[i], "_Tumor")] <- tumor_color
    }
  }

  p <- ggplot(plot_data, aes(x = factor(get(group_var)), y = trmt10c_expression,
                            fill = combined_group)) +
    geom_boxplot(alpha = 0.8, outlier.shape = 16, outlier.size = 0.8,
                position = position_dodge(width = 0.8)) +
    geom_point(aes(color = sample_type),
              position = position_jitterdodge(dodge.width = 0.8, jitter.width = 0.2),
              alpha = 0.6, size = 0.8) +
    scale_fill_manual(values = color_palette) +
    scale_color_manual(values = c("Normal" = "#2980b9", "Tumor" = "#c0392b")) +
    labs(title = title,
         x = gsub("_", " ", group_var),
         y = "TRMT10C Expression") +
    theme_classic() +
    theme(
      plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
      axis.text.x = element_text(angle = 45, hjust = 1),
      legend.title = element_blank(),
      legend.position = "bottom"
    ) +
    guides(fill = guide_legend(ncol = 4))

  return(p)
}

# 7. 基础差异分析图：正常 vs 肿瘤
print("=== 生成基础差异分析图 ===")

# 正常vs肿瘤箱线图
basic_data <- merged_data[!is.na(merged_data$trmt10c_expression), ]

p1 <- ggplot(basic_data, aes(x = sample_type, y = trmt10c_expression, fill = sample_type)) +
  geom_boxplot(alpha = 0.7, outlier.shape = 16, outlier.size = 1) +
  geom_jitter(width = 0.2, alpha = 0.5, size = 0.8) +
  scale_fill_manual(values = c("Normal" = "#3498db", "Tumor" = "#e74c3c")) +
  labs(title = "TRMT10C Expression: Normal vs Tumor",
       x = "Sample Type",
       y = "TRMT10C Expression") +
  theme_classic() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    axis.text.x = element_text(size = 12),
    legend.position = "none"
  ) +
  stat_compare_means(method = "wilcox.test",
                    label.y = max(basic_data$trmt10c_expression, na.rm = TRUE) * 1.1)

ggsave("TRMT10C_Normal_vs_Tumor.png", p1, width = 8, height = 6, dpi = 300)
print("已保存: TRMT10C_Normal_vs_Tumor.png")

# 8. 按分期分别生成图表 - 方案1：每个亚组包含正常vs肿瘤对比
print("=== 按分期生成图表（方案1：正常vs肿瘤对比）===")

stages <- c("Stage I", "Stage II", "Stage III", "Stage IV")

for (stage in stages) {
  print(paste("=== 处理", stage, "==="))

  # 获取当前分期的所有样本（包括正常和肿瘤）
  stage_data <- merged_data[merged_data$stage_simple == stage &
                           !is.na(merged_data$stage_simple), ]

  normal_count <- sum(stage_data$sample_type == "Normal", na.rm = TRUE)
  tumor_count <- sum(stage_data$sample_type == "Tumor", na.rm = TRUE)

  print(paste(stage, "样本数: 正常", normal_count, "个, 肿瘤", tumor_count, "个"))

  if (normal_count == 0 || tumor_count == 0) {
    print(paste("跳过", stage, "- 缺少正常或肿瘤样本"))
    next
  }

  # 年龄分组分析
  print("生成年龄分组图...")
  age_plots <- create_subgroup_boxplot(
    data = stage_data,
    subgroup_var = "age_group",
    title_prefix = paste("TRMT10C Expression by Age Group -", stage),
    min_normal_samples = 2
  )

  if (length(age_plots) > 0) {
    # 保存每个年龄组的图
    for (age_group in names(age_plots)) {
      filename <- paste0("TRMT10C_", gsub(" ", "_", stage), "_Age_",
                        gsub("[<>-]", "", age_group), ".png")
      ggsave(filename, age_plots[[age_group]], width = 8, height = 6, dpi = 300)
      print(paste("已保存:", filename))
    }
  }

  # 性别分组分析
  print("生成性别分组图...")
  gender_plots <- create_subgroup_boxplot(
    data = stage_data,
    subgroup_var = "gender",
    title_prefix = paste("TRMT10C Expression by Gender -", stage),
    min_normal_samples = 2
  )

  if (length(gender_plots) > 0) {
    for (gender in names(gender_plots)) {
      filename <- paste0("TRMT10C_", gsub(" ", "_", stage), "_Gender_", gender, ".png")
      ggsave(filename, gender_plots[[gender]], width = 8, height = 6, dpi = 300)
      print(paste("已保存:", filename))
    }
  }

  # 生存状态分组分析
  print("生成生存状态分组图...")
  survival_plots <- create_subgroup_boxplot(
    data = stage_data,
    subgroup_var = "vital_status",
    title_prefix = paste("TRMT10C Expression by Survival Status -", stage),
    min_normal_samples = 2
  )

  if (length(survival_plots) > 0) {
    for (status in names(survival_plots)) {
      filename <- paste0("TRMT10C_", gsub(" ", "_", stage), "_Survival_", status, ".png")
      ggsave(filename, survival_plots[[status]], width = 8, height = 6, dpi = 300)
      print(paste("已保存:", filename))
    }
  }

  print(paste("完成", stage, "的分析"))
  print("---")
}

# 9. 综合分析图：所有样本的亚组分析（方案1：正常vs肿瘤对比）
print("=== 生成综合亚组分析图（方案1：正常vs肿瘤对比）===")

# 年龄分组综合分析
print("生成年龄分组综合分析图...")
p_comprehensive_age <- create_comprehensive_boxplot(
  data = merged_data,
  group_var = "age_group",
  title = "TRMT10C Expression by Age Group (Normal vs Tumor)"
)

if (!is.null(p_comprehensive_age)) {
  ggsave("TRMT10C_Comprehensive_Age_Groups.png", p_comprehensive_age,
         width = 12, height = 8, dpi = 300)
  print("已保存: TRMT10C_Comprehensive_Age_Groups.png")
}

# 性别分组综合分析
print("生成性别分组综合分析图...")
p_comprehensive_gender <- create_comprehensive_boxplot(
  data = merged_data,
  group_var = "gender",
  title = "TRMT10C Expression by Gender (Normal vs Tumor)"
)

if (!is.null(p_comprehensive_gender)) {
  ggsave("TRMT10C_Comprehensive_Gender.png", p_comprehensive_gender,
         width = 10, height = 8, dpi = 300)
  print("已保存: TRMT10C_Comprehensive_Gender.png")
}

# 生存状态综合分析
print("生成生存状态综合分析图...")
p_comprehensive_survival <- create_comprehensive_boxplot(
  data = merged_data,
  group_var = "vital_status",
  title = "TRMT10C Expression by Survival Status (Normal vs Tumor)"
)

if (!is.null(p_comprehensive_survival)) {
  ggsave("TRMT10C_Comprehensive_Survival.png", p_comprehensive_survival,
         width = 10, height = 8, dpi = 300)
  print("已保存: TRMT10C_Comprehensive_Survival.png")
}

# 分期综合分析
print("生成分期综合分析图...")
p_comprehensive_stage <- create_comprehensive_boxplot(
  data = merged_data,
  group_var = "stage_simple",
  title = "TRMT10C Expression by Stage (Normal vs Tumor)"
)

if (!is.null(p_comprehensive_stage)) {
  ggsave("TRMT10C_Comprehensive_Stage.png", p_comprehensive_stage,
         width = 12, height = 8, dpi = 300)
  print("已保存: TRMT10C_Comprehensive_Stage.png")
}

# 10. 生成分析报告
print("=== 生成分析报告 ===")

# 统计分析结果
analysis_results <- list()

# 正常vs肿瘤
normal_expr <- merged_data$trmt10c_expression[merged_data$sample_type == "Normal"]
tumor_expr <- merged_data$trmt10c_expression[merged_data$sample_type == "Tumor"]
wilcox_result <- wilcox.test(normal_expr, tumor_expr)

analysis_results$normal_vs_tumor <- list(
  normal_median = median(normal_expr, na.rm = TRUE),
  tumor_median = median(tumor_expr, na.rm = TRUE),
  p_value = wilcox_result$p.value,
  effect_size = median(tumor_expr, na.rm = TRUE) / median(normal_expr, na.rm = TRUE)
)

# 保存分析结果
save(analysis_results, file = "TRMT10C_analysis_results.rda")

print("=== 分析完成 ===")
print("生成的文件:")
print("数据文件:")
print("- TRMT10C_integrated_data.tsv")
print("- TRMT10C_tumor_data.tsv")
print("- TRMT10C_expression_matrix.tsv")
print("- TRMT10C_analysis_results.rda")
print("")
print("图表文件:")
print("- TRMT10C_Normal_vs_Tumor.png")
print("- TRMT10C_All_Tumors_*.png (综合分析)")
print("- TRMT10C_Stage_*_*.png (按分期分析)")
print("")
print("分析总结:")
print(paste("正常样本TRMT10C表达中位数:", round(analysis_results$normal_vs_tumor$normal_median, 2)))
print(paste("肿瘤样本TRMT10C表达中位数:", round(analysis_results$normal_vs_tumor$tumor_median, 2)))
print(paste("Wilcoxon检验p值:", format(analysis_results$normal_vs_tumor$p_value, scientific = TRUE)))
print(paste("效应大小(肿瘤/正常):", round(analysis_results$normal_vs_tumor$effect_size, 3)))
