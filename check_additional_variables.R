# 检查TCGA LUSC数据中可用的额外临床变量
setwd("C:/Users/<USER>/Desktop/code/TCGA/肺癌")

library(TCGAbiolinks)
library(dplyr)

# 获取完整的临床数据
clinical <- GDCquery_clinic(project = "TCGA-LUSC", type = "clinical")

print("=== 所有可用的临床变量 ===")
print(colnames(clinical))

print("\n=== 检查吸烟相关变量 ===")
smoking_cols <- colnames(clinical)[grep("smok|tobacco", colnames(clinical), ignore.case = TRUE)]
print("吸烟相关列:")
print(smoking_cols)

if(length(smoking_cols) > 0) {
  for(col in smoking_cols) {
    print(paste("===", col, "==="))
    print(table(clinical[[col]], useNA = "always"))
  }
}

print("\n=== 检查组织学相关变量 ===")
histology_cols <- colnames(clinical)[grep("histolog|grade|differentiat", colnames(clinical), ignore.case = TRUE)]
print("组织学相关列:")
print(histology_cols)

if(length(histology_cols) > 0) {
  for(col in histology_cols) {
    print(paste("===", col, "==="))
    print(table(clinical[[col]], useNA = "always"))
  }
}

print("\n=== 检查TNM分期变量 ===")
tnm_cols <- colnames(clinical)[grep("ajcc.*[tnm]|pathologic.*[tnm]|clinical.*[tnm]", colnames(clinical), ignore.case = TRUE)]
print("TNM相关列:")
print(tnm_cols)

if(length(tnm_cols) > 0) {
  for(col in tnm_cols) {
    print(paste("===", col, "==="))
    print(table(clinical[[col]], useNA = "always"))
  }
}

print("\n=== 检查治疗相关变量 ===")
treatment_cols <- colnames(clinical)[grep("treatment|therapy|drug|radiation|surgery", colnames(clinical), ignore.case = TRUE)]
print("治疗相关列:")
print(treatment_cols)

if(length(treatment_cols) > 0) {
  for(col in treatment_cols) {
    print(paste("===", col, "==="))
    print(table(clinical[[col]], useNA = "always"))
  }
}

print("\n=== 检查生存相关变量 ===")
survival_cols <- colnames(clinical)[grep("days.*death|days.*follow|survival|recurrence", colnames(clinical), ignore.case = TRUE)]
print("生存相关列:")
print(survival_cols)

if(length(survival_cols) > 0) {
  for(col in survival_cols) {
    print(paste("===", col, "==="))
    if(is.numeric(clinical[[col]])) {
      print(summary(clinical[[col]]))
    } else {
      print(table(clinical[[col]], useNA = "always"))
    }
  }
}

print("\n=== 检查其他重要变量 ===")
other_cols <- colnames(clinical)[grep("bmi|weight|height|alcohol|family|prior", colnames(clinical), ignore.case = TRUE)]
print("其他重要列:")
print(other_cols)

if(length(other_cols) > 0) {
  for(col in other_cols) {
    print(paste("===", col, "==="))
    if(is.numeric(clinical[[col]])) {
      print(summary(clinical[[col]]))
    } else {
      print(table(clinical[[col]], useNA = "always"))
    }
  }
}

# 检查数据完整性
print("\n=== 数据完整性评估 ===")

# 计算每列的非缺失值数量
non_missing_counts <- sapply(clinical, function(x) sum(!is.na(x)))
total_rows <- nrow(clinical)
completeness_pct <- round(non_missing_counts / total_rows * 100, 1)

# 创建完整性数据框
completeness <- data.frame(
  variable = names(non_missing_counts),
  non_missing = non_missing_counts,
  total = total_rows,
  completeness_pct = completeness_pct,
  stringsAsFactors = FALSE
)

# 按完整性排序
completeness <- completeness[order(completeness$completeness_pct, decreasing = TRUE), ]

print("变量完整性排序 (前20个):")
print(head(completeness, 20))

# 保存结果
write.csv(completeness, "clinical_variables_completeness.csv", row.names = FALSE)
print("\n变量完整性已保存到: clinical_variables_completeness.csv")
