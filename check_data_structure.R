# 检查现有TCGA LUSC数据结构
# 专门针对TRMT10C基因分析

setwd("C:/Users/<USER>/Desktop/code/TCGA/肺癌")

library(TCGAbiolinks)
library(SummarizedExperiment)

# 加载已有的数据
print("=== 加载数据 ===")
load("TCGA_LUSC_RNAseq_hg38.rda")

# 检查数据对象名称
print("当前环境中的对象:")
print(ls())

# 如果数据对象名称是data，使用data；否则查找SummarizedExperiment对象
if(exists("data")) {
  se_data <- data
} else {
  # 查找SummarizedExperiment对象
  se_objects <- ls()[sapply(ls(), function(x) class(get(x))[1] == "RangedSummarizedExperiment")]
  if(length(se_objects) > 0) {
    se_data <- get(se_objects[1])
    print(paste("使用对象:", se_objects[1]))
  } else {
    stop("未找到SummarizedExperiment对象")
  }
}

print("=== 数据基本信息 ===")
print(paste("数据维度:", nrow(se_data), "x", ncol(se_data)))
print("数据类型:")
print(class(se_data))

# 处理基因名称
print("=== 处理基因名称 ===")
if(!"gene_name" %in% colnames(rowData(se_data))) {
  print("rowData列名:")
  print(colnames(rowData(se_data)))
} else {
  rownames(se_data) <- rowData(se_data)$gene_name
  se_data <- se_data[!duplicated(rownames(se_data)),]
  se_data <- se_data[rowData(se_data)$gene_type=="protein_coding",]
  print(paste("处理后数据维度:", nrow(se_data), "x", ncol(se_data)))
}

# 检查TRMT10C基因
print("=== 检查TRMT10C基因 ===")
if("TRMT10C" %in% rownames(se_data)) {
  print("✓ TRMT10C基因存在于数据中")
  trmt10c_expr <- assay(se_data, "unstranded")["TRMT10C", ]
  print(paste("TRMT10C表达范围:", round(min(trmt10c_expr), 2), "-", round(max(trmt10c_expr), 2)))
  print(paste("TRMT10C表达中位数:", round(median(trmt10c_expr), 2)))
} else {
  print("TRMT10C基因不存在，检查相似基因...")
  trmt_genes <- rownames(se_data)[grep("TRMT", rownames(se_data), ignore.case = TRUE)]
  print("包含TRMT的基因:")
  print(trmt_genes)
}

# 样本类型分析
print("=== 样本类型分析 ===")
# 从样本条码中提取样本类型
sample_barcodes <- colnames(se_data)
sample_types <- substr(sample_barcodes, 14, 15)
print("样本类型分布:")
print(table(sample_types))

samplesNT <- TCGAquery_SampleTypes(barcode = colnames(se_data), typesample = c("NT"))
samplesTP <- TCGAquery_SampleTypes(barcode = colnames(se_data), typesample = c("TP"))
print(paste("正常样本数:", length(samplesNT)))
print(paste("肿瘤样本数:", length(samplesTP)))

# 获取临床数据
print("=== 获取临床数据 ===")
clinical <- GDCquery_clinic(project = "TCGA-LUSC", type = "clinical")
print(paste("临床数据维度:", nrow(clinical), "x", ncol(clinical)))

print("=== 临床数据列名 ===")
print(colnames(clinical))

# 检查关键临床变量
print("=== 年龄信息 ===")
age_cols <- colnames(clinical)[grep("age", colnames(clinical), ignore.case = TRUE)]
print("包含age的列:")
print(age_cols)
if("age_at_index" %in% colnames(clinical)) {
  print("年龄统计 (age_at_index):")
  print(summary(clinical$age_at_index))
  # 年龄分组
  clinical$age_group <- cut(clinical$age_at_index,
                           breaks = c(0, 60, 70, 100),
                           labels = c("<60", "60-70", ">70"),
                           include.lowest = TRUE)
  print("年龄分组分布:")
  print(table(clinical$age_group, useNA = "always"))
}

print("=== 分期信息 ===")
stage_cols <- colnames(clinical)[grep("stage", colnames(clinical), ignore.case = TRUE)]
print("包含stage的列:")
print(stage_cols)
if("ajcc_pathologic_stage" %in% colnames(clinical)) {
  print("AJCC病理分期分布:")
  print(table(clinical$ajcc_pathologic_stage, useNA = "always"))
}

print("=== 性别信息 ===")
if("gender" %in% colnames(clinical)) {
  print("性别分布:")
  print(table(clinical$gender, useNA = "always"))
}

print("=== 人种信息 ===")
race_cols <- colnames(clinical)[grep("race", colnames(clinical), ignore.case = TRUE)]
print("包含race的列:")
print(race_cols)
if(length(race_cols) > 0) {
  for(col in race_cols) {
    print(paste("===", col, "==="))
    print(table(clinical[[col]], useNA = "always"))
  }
}

print("=== 生存状态信息 ===")
vital_cols <- colnames(clinical)[grep("vital|death|survival|status", colnames(clinical), ignore.case = TRUE)]
print("包含生存相关的列:")
print(vital_cols)
if(length(vital_cols) > 0) {
  for(col in vital_cols) {
    print(paste("===", col, "==="))
    print(table(clinical[[col]], useNA = "always"))
  }
}

# 保存检查结果
print("=== 保存数据结构信息 ===")
data_info <- list(
  gene_exists = "TRMT10C" %in% rownames(se_data),
  normal_samples = length(samplesNT),
  tumor_samples = length(samplesTP),
  clinical_cols = colnames(clinical),
  age_cols = age_cols,
  stage_cols = stage_cols,
  race_cols = race_cols,
  vital_cols = vital_cols
)

save(data_info, file = "data_structure_info.rda")
print("数据结构信息已保存到 data_structure_info.rda")
