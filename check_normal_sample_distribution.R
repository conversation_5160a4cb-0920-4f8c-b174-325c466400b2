# 检查正常样本在各个亚组中的分布
setwd("C:/Users/<USER>/Desktop/code/TCGA/肺癌")

# 加载数据
library(TCGAbiolinks)
library(SummarizedExperiment)
load("TCGA_LUSC_RNAseq_hg38.rda")

# 处理数据
rownames(data) <- rowData(data)$gene_name
data <- data[!duplicated(rownames(data)),]
data <- data[rowData(data)$gene_type=="protein_coding",]

# 获取表达矩阵
exp_matrix <- assay(data, "unstranded")
trmt10c_expr <- exp_matrix["TRMT10C", ]

# 获取样本类型
samplesNT <- TCGAquery_SampleTypes(barcode = colnames(exp_matrix), typesample = c("NT"))
samplesTP <- TCGAquery_SampleTypes(barcode = colnames(exp_matrix), typesample = c("TP"))

# 获取临床数据
clinical <- GDCquery_clinic(project = "TCGA-LUSC", type = "clinical")
clinical$sample_id <- substr(clinical$submitter_id, 1, 12)

# 处理年龄分组
clinical$age_group <- cut(clinical$age_at_index,
                         breaks = c(0, 60, 70, 100),
                         labels = c("<60", "60-70", ">70"),
                         include.lowest = TRUE)

# 处理分期信息
clinical$stage_simple <- clinical$ajcc_pathologic_stage
clinical$stage_simple[clinical$stage_simple %in% c("Stage I", "Stage IA", "Stage IB")] <- "Stage I"
clinical$stage_simple[clinical$stage_simple %in% c("Stage II", "Stage IIA", "Stage IIB")] <- "Stage II"
clinical$stage_simple[clinical$stage_simple %in% c("Stage III", "Stage IIIA", "Stage IIIB")] <- "Stage III"
clinical$stage_simple[clinical$stage_simple == "Stage IV"] <- "Stage IV"

# 创建样本信息数据框
sample_info <- data.frame(
  sample_barcode = names(trmt10c_expr),
  trmt10c_expression = as.numeric(trmt10c_expr),
  sample_type = ifelse(names(trmt10c_expr) %in% samplesNT, "Normal", "Tumor"),
  stringsAsFactors = FALSE
)

sample_info$sample_id <- substr(sample_info$sample_barcode, 1, 12)

# 合并临床数据
merged_data <- merge(sample_info, clinical, by = "sample_id", all.x = TRUE)

print("=== 正常样本在各亚组中的分布 ===")

# 正常样本总数
normal_data <- merged_data[merged_data$sample_type == "Normal", ]
print(paste("正常样本总数:", nrow(normal_data)))

print("\n=== 年龄分组中的正常样本分布 ===")
age_normal_table <- table(normal_data$age_group, useNA = "always")
print(age_normal_table)

print("\n=== 性别分组中的正常样本分布 ===")
gender_normal_table <- table(normal_data$gender, useNA = "always")
print(gender_normal_table)

print("\n=== 人种分组中的正常样本分布 ===")
race_normal_table <- table(normal_data$race, useNA = "always")
print(race_normal_table)

print("\n=== 生存状态中的正常样本分布 ===")
vital_normal_table <- table(normal_data$vital_status, useNA = "always")
print(vital_normal_table)

print("\n=== 分期分组中的正常样本分布 ===")
stage_normal_table <- table(normal_data$stage_simple, useNA = "always")
print(stage_normal_table)

print("\n=== 详细的年龄分组分析 ===")
for(age_group in c("<60", "60-70", ">70")) {
  normal_count <- sum(normal_data$age_group == age_group, na.rm = TRUE)
  tumor_count <- sum(merged_data$sample_type == "Tumor" &
                    merged_data$age_group == age_group, na.rm = TRUE)
  print(paste(age_group, "组: 正常", normal_count, "个, 肿瘤", tumor_count, "个"))
}

print("\n=== 详细的性别分组分析 ===")
for(gender in c("female", "male")) {
  normal_count <- sum(normal_data$gender == gender, na.rm = TRUE)
  tumor_count <- sum(merged_data$sample_type == "Tumor" &
                    merged_data$gender == gender, na.rm = TRUE)
  print(paste(gender, "组: 正常", normal_count, "个, 肿瘤", tumor_count, "个"))
}

print("\n=== 详细的分期分组分析 ===")
for(stage in c("Stage I", "Stage II", "Stage III", "Stage IV")) {
  normal_count <- sum(normal_data$stage_simple == stage, na.rm = TRUE)
  tumor_count <- sum(merged_data$sample_type == "Tumor" &
                    merged_data$stage_simple == stage, na.rm = TRUE)
  print(paste(stage, "组: 正常", normal_count, "个, 肿瘤", tumor_count, "个"))
}

print("\n=== 建议的分析策略 ===")
print("基于样本数量，建议的分析策略:")

# 年龄分组建议
print("\n年龄分组:")
for(age_group in c("<60", "60-70", ">70")) {
  normal_count <- sum(normal_data$age_group == age_group, na.rm = TRUE)
  if(normal_count >= 5) {
    print(paste("✓", age_group, "组: 正常样本充足(", normal_count, "个), 可进行对比分析"))
  } else if(normal_count >= 2) {
    print(paste("△", age_group, "组: 正常样本较少(", normal_count, "个), 可分析但需谨慎解释"))
  } else {
    print(paste("✗", age_group, "组: 正常样本过少(", normal_count, "个), 建议跳过或合并"))
  }
}

# 性别分组建议
print("\n性别分组:")
for(gender in c("female", "male")) {
  normal_count <- sum(normal_data$gender == gender, na.rm = TRUE)
  if(normal_count >= 5) {
    print(paste("✓", gender, "组: 正常样本充足(", normal_count, "个), 可进行对比分析"))
  } else if(normal_count >= 2) {
    print(paste("△", gender, "组: 正常样本较少(", normal_count, "个), 可分析但需谨慎解释"))
  } else {
    print(paste("✗", gender, "组: 正常样本过少(", normal_count, "个), 建议跳过或合并"))
  }
}

# 分期分组建议
print("\n分期分组:")
for(stage in c("Stage I", "Stage II", "Stage III", "Stage IV")) {
  normal_count <- sum(normal_data$stage_simple == stage, na.rm = TRUE)
  if(normal_count >= 5) {
    print(paste("✓", stage, "组: 正常样本充足(", normal_count, "个), 可进行对比分析"))
  } else if(normal_count >= 2) {
    print(paste("△", stage, "组: 正常样本较少(", normal_count, "个), 可分析但需谨慎解释"))
  } else {
    print(paste("✗", stage, "组: 正常样本过少(", normal_count, "个), 建议跳过或合并"))
  }
}
