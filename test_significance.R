# 测试显著性检验功能
setwd("C:/Users/<USER>/Desktop/code/TCGA/肺癌")

library(ggplot2)
library(dplyr)

# 加载数据
load("TCGA_LUSC_RNAseq_hg38.rda")

# 简单的数据处理
rownames(data) <- rowData(data)$gene_name
data <- data[!duplicated(rownames(data)), ]
exp_matrix <- assay(data, "unstranded")

# 获取TRMT10C表达数据
trmt10c_expr <- exp_matrix["TRMT10C", ]
sample_info <- data.frame(
  sample_barcode = names(trmt10c_expr),
  trmt10c_expression = as.numeric(trmt10c_expr),
  sample_type = ifelse(grepl("-11A-", names(trmt10c_expr)), "Normal", "Tumor"),
  stringsAsFactors = FALSE
)

# 创建测试数据 - 年龄分组
set.seed(123)
sample_info$age_group <- sample(c("<60", "60-70", ">70"), nrow(sample_info), replace = TRUE)

# 简化的显著性检验函数
add_simple_significance <- function(plot_obj, data, x_var) {
  subgroups <- unique(data[[x_var]])
  subgroups <- subgroups[!is.na(subgroups)]
  
  y_max <- max(data$trmt10c_expression, na.rm = TRUE)
  
  for(i in seq_along(subgroups)) {
    subgroup_data <- data[data[[x_var]] == subgroups[i], ]
    
    normal_data <- subgroup_data$trmt10c_expression[subgroup_data$sample_type == "Normal"]
    tumor_data <- subgroup_data$trmt10c_expression[subgroup_data$sample_type == "Tumor"]
    
    normal_data <- normal_data[!is.na(normal_data)]
    tumor_data <- tumor_data[!is.na(tumor_data)]
    
    if(length(normal_data) > 2 && length(tumor_data) > 2) {
      test_result <- wilcox.test(normal_data, tumor_data)
      p_val <- test_result$p.value
      
      if(p_val < 0.001) {
        p_text <- "***"
      } else if(p_val < 0.01) {
        p_text <- "**"
      } else if(p_val < 0.05) {
        p_text <- "*"
      } else {
        p_text <- "ns"
      }
      
      plot_obj <- plot_obj + 
        annotate("text", x = i, y = y_max * 1.2,
                label = p_text, size = 4, fontface = "bold")
    }
  }
  
  return(plot_obj)
}

# 创建测试图
p_test <- ggplot(sample_info, aes(x = age_group, y = trmt10c_expression, fill = sample_type)) +
  geom_boxplot(position = position_dodge(width = 0.8), alpha = 0.8) +
  scale_fill_manual(values = c("Normal" = "#2E86AB", "Tumor" = "#A23B72")) +
  labs(title = "Test Significance Display") +
  theme_classic()

# 添加显著性检验
p_test <- add_simple_significance(p_test, sample_info, "age_group")

# 保存测试图
ggsave("test_significance.png", p_test, width = 8, height = 6, dpi = 300)

print("测试完成，已保存 test_significance.png")

# 检查每个亚组的样本数
print("各亚组样本数:")
print(table(sample_info$age_group, sample_info$sample_type))
